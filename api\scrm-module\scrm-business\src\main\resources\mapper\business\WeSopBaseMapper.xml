<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeSopBaseMapper">

    <resultMap id="WeSopBaseResultMap" type="org.scrm.domain.sop.WeSopBase">
        <id property="id" column="id"/>
        <result property="baseType" column="base_type"/>
        <result property="sopName" column="sop_name"/>
        <result property="sopState" column="sop_state"/>
        <result property="sendType" column="send_type"/>
        <result property="executeWeUser" column="execute_we_user" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="executeWeUserIds" column="execute_we_user_ids"/>
        <result property="executeCustomerOrGroup" column="execute_customer_or_group" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="executeCustomerSwipe" column="execute_customer_swipe" typeHandler="org.scrm.typeHandler.WeStrategicCrowdSwipeListTypeHandler"/>
        <result property="earlyEnd" column="early_end"/>
        <result property="endContent" column="end_content" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="weCustomersQuery" column="we_customers_query" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>


    <select id="findWeSops" resultMap="WeSopBaseResultMap">
        SELECT
            id,
            base_type,
            business_type,
            sop_name,
            sop_state,
            send_type,
            execute_we_user,
            execute_we_user_ids,
            execute_customer_or_group,
            execute_customer_swipe,
            early_end,
            end_content,
            create_by,
            create_by_id,
            create_time,
            update_by,
            update_by_id,
            update_time,
            del_flag,
            we_customers_query,
            scope_type
        FROM
            we_sop_base
        ${ew.customSqlSegment}
    </select>

    <select id="findWeSopDetailTabVo" resultType="org.scrm.domain.sop.vo.WeSopDetailTabVo">
        SELECT
            count(IF(
                        date_format(update_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
                ,1,null)) as tdCustomerNumber,
            count(IF(execute_state=3
                         and date_format(update_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
                ,1,null)) as tdCommonEndCustomerNumber,
            count(IF(execute_state=2
                         and date_format(update_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
                ,1,null)) as tdEarlyEndCustomerNumber,
            count(IF(execute_state=4
                         and date_format(update_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
                ,1,null)) as tdErrorEndCustomerNumber,

            count(IF(
                        date_format(update_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')
                ,1,null)) as ydCustomerNumber,
            count(IF(execute_state=3
                         and date_format(update_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')
                ,1,null)) as ydCommonEndCustomerNumber,
            count(IF(execute_state=2
                         and date_format(create_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')
                ,1,null)) as ydEarlyEndCustomerNumber,
            count(IF(execute_state=4
                         and date_format(update_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')
                ,1,null)) as ydErrorEndCustomerNumber
        FROM
            we_sop_execute_target
        where del_flag=0 and sop_base_id=#{sopBaseId}
    </select>

    <select id="findWeSopDetailCustomer" resultType="org.scrm.domain.sop.vo.WeSopDetailCustomerVo">
        SELECT
        wset.id as executeTargetId,
        wc.customer_name,
        wc.corp_name as corpName,
        wc.gender,
        wc.customer_type,
        wc.avatar,
        wc.external_userid,
        wset.execute_state,
        su.we_user_id,
        su.user_name,
        su.position,
        (SELECT GROUP_CONCAT(DISTINCT sd.dept_name) FROM sys_dept sd LEFT JOIN sys_user_dept sud ON sd.dept_id=sud.dept_id where sud.user_id=su.user_id and sud.del_flag='0') as deptName,
        wset.create_time,
        wset.execute_end_time as executeEndTime,
        wset.sop_base_id,
        IFNULL(ROUND((SELECT COUNT(*) FROM we_sop_execute_target_attachments wseta WHERE wseta.execute_target_id=wset.id and execute_state=1)/(SELECT COUNT(*) FROM we_sop_execute_target_attachments wseta WHERE wseta.execute_target_id=wset.id )*100,0),0) as efficiency
        FROM
        we_sop_execute_target wset
        LEFT JOIN we_customer wc ON wset.target_id=wc.external_userid
        LEFT JOIN sys_user su ON su.we_user_id=wset.execute_we_user_id
        <where>
            <if test="sopBaseId != null and sopBaseId !=''">
                wset.sop_base_id=#{sopBaseId}
            </if>
            <if test="customerName !=null and customerName!=''">
                and wc.customer_name like concat( '%' , #{customerName}, '%')
            </if>
            <if test="executeState !=null">
                and wset.execute_state=#{executeState}
            </if>
            <if test="weUserId !=null and weUserId!=''">
                AND su.we_user_id=#{weUserId}
            </if>
        </where>
        GROUP BY wset.id
        ORDER BY wset.create_time DESC
    </select>

    <select id="findWeSopDetailGroup" resultType="org.scrm.domain.sop.vo.WeSopDetailGroupVo">
        SELECT
        wset.id as executeTargetId,
        wg.group_name,
        wg.chat_id,
        wset.execute_state,
        su.we_user_id,
        su.user_name,
        su.position,
        (
                SELECT
                    GROUP_CONCAT( DISTINCT sd.dept_name )
                FROM
                sys_user su
                LEFT JOIN sys_user_dept sud ON sud.user_id = su.user_id
                LEFT JOIN sys_dept sd ON sd.dept_id = sud.dept_id
                WHERE
                sud.we_user_id=wset.execute_we_user_id
                AND sud.del_flag = '0'
        ) as deptName,
        wset.create_time,
        wset.execute_end_time,
        wset.sop_base_id,
        IFNULL(ROUND((SELECT COUNT(*) FROM we_sop_execute_target_attachments wseta WHERE wseta.execute_target_id=wset.id  and execute_state=1)/(SELECT COUNT(*) FROM we_sop_execute_target_attachments wseta WHERE wseta.execute_target_id=wset.id)*100,0),0) as efficiency
        FROM
        we_sop_execute_target wset
        LEFT JOIN we_group wg ON wset.target_id=wg.chat_id
        LEFT JOIN sys_user su ON su.we_user_id=wset.execute_we_user_id
        <where>
            <if test="sopBaseId != null and sopBaseId !=''">
                wset.sop_base_id=#{sopBaseId}
            </if>
            <if test="groupName !=null and groupName!=''">
                and  wg.group_name  like concat( '%' , #{groupName}, '%')
            </if>
            <if test="executeState !=null">
                and wset.execute_state=#{executeState}
            </if>
            <if test="weUserId !=null and weUserId!=''">
                AND su.we_user_id in
                <foreach collection="weUserId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY wset.create_time DESC
    </select>

    <select id="findCustomerExecuteContent" resultType="org.scrm.domain.sop.vo.content.WeCustomerSopBaseContentVo">
        SELECT
        wseca.push_time_type,
        wseca.push_time_pre,
        wseca.push_day_number,
        IFNULL(wseca.push_start_time,now()) as push_start_time ,
        IFNULL(wseca.push_end_time,NOW()) as push_end_time,
        wseca.execute_state,
        wseca.execute_time,
        wseca.sop_attachment_id,
        wc.customer_name,
        wc.customer_type,
        wc.external_userid,
        wc.gender,
        wc.avatar,
        wc.phone,
        wset.sop_base_id,
        wsb.sop_name,
        wsb.business_type,
        wseca.id as  executeTargetAttachId
        FROM
        we_sop_execute_target_attachments wseca
        LEFT JOIN we_sop_execute_target wset ON wseca.execute_target_id=wset.id
        LEFT JOIN we_sop_base wsb ON wsb.id=wset.sop_base_id
        LEFT JOIN we_customer wc ON wc.external_userid=wset.target_id
        <if test="executeWeUserId !='' and executeWeUserId !=null ">
            and wc.add_user_id=#{executeWeUserId}
        </if>
        WHERE
        wsb.del_flag=0
        <if test="sopState !=null">
            and wsb.sop_state=#{sopState}
        </if>
        <if test="!checkAll">
            and  to_days(wseca.push_start_time) = to_days(now()) and  wsb.send_type=2
        </if>
        <if test="executeWeUserId !='' and executeWeUserId !=null ">
            and wset.execute_we_user_id=#{executeWeUserId}
        </if>
        <if test="targetId !='' and targetId !=null">
            and wset.target_id=#{targetId}
        </if>
        <if test="executeSubState != null">
            and wseca.execute_state=#{executeSubState}
        </if>
        <if test="executeSubState == 0">
            and wset.execute_state=1
        </if>
        <if test="sopBaseId !=null">
            and wset.sop_base_id=#{sopBaseId}
        </if>
        <if test="executeTargetId != null and executeTargetId !=''">
            and wseca.execute_target_id=#{executeTargetId}
        </if>
    </select>

    <select id="findGroupExecuteContent" resultType="org.scrm.domain.sop.vo.content.WeGroupSopBaseContentVo">
        SELECT
        wseca.push_time_type,
        wseca.push_time_pre,
        wseca.push_day_number,
        IFNULL(wseca.push_start_time,now()) as push_start_time ,
        IFNULL(wseca.push_end_time,NOW()) as push_end_time,
        wseca.execute_state,
        wseca.sop_attachment_id,
        wseca.execute_time,
        wg.group_name,
        wg.chat_id,
        wg.add_time,
        wset.sop_base_id,
        wsb.sop_name,
        wseca.id as  executeTargetAttachId
        FROM
        we_sop_execute_target_attachments wseca
        LEFT JOIN we_sop_execute_target wset ON wseca.execute_target_id=wset.id
        LEFT JOIN we_sop_base wsb ON wsb.id=wset.sop_base_id
        LEFT JOIN we_group wg ON wg.chat_id=wset.target_id
        <where>
            <if test="sopState != null">
                wsb.sop_state=#{sopState}
            </if>
        <if test="!checkAll">
            and  to_days(wseca.push_start_time) = to_days(now()) and  wsb.send_type=2
        </if>
        <if test="chatId !='' and chatId !=null">
            and  wg.chat_id=#{chatId}
        </if>
        <if test="executeSubState != null">
            and wseca.execute_state=#{executeSubState}
        </if>

        <if test="executeSubState == 0">
            and wset.execute_state=1
        </if>
        <if test="sopBaseId !=null">
            and wset.sop_base_id=#{sopBaseId}
        </if>
        <if test="executeTargetId != null and executeTargetId !=''">
            and wseca.execute_target_id=#{executeTargetId}
        </if>
        <if test="executeWeUserId != null and executeWeUserId !=''">
            and wset.execute_we_user_id=#{executeWeUserId}
        </if>
        </where>
    </select>

    <select id="findTdSendSopCustomers" resultType="org.scrm.domain.sop.vo.content.WeCustomerSopToBeSentVo">
        SELECT
            wc.customer_name,
            wc.customer_type,
            wc.gender,
            wc.avatar,
            wc.external_userid,
            wc.corp_name,
            wc.phone
        FROM
            we_sop_execute_target_attachments wseta
                LEFT JOIN we_sop_execute_target wset ON wset.id = wseta.execute_target_id
                LEFT JOIN we_customer wc ON wc.external_userid = wset.target_id
                LEFT JOIN we_sop_base wsb on wsb.id=wset.sop_base_id
        WHERE
            wc.add_user_id = #{weUserId}  AND wset.target_type = 1 and wc.del_flag=0 and wset.execute_state=1
            and wsb.del_flag=0  and wsb.sop_state=1 and wsb.send_type=2
        <choose>
            <when test="isExpiringSoon">
                AND   timestampdiff(MINUTE,NOW(),wseta.push_end_time) BETWEEN 0 AND 10
            </when>
            <otherwise>
                AND to_days( wseta.push_start_time ) = to_days(now())
            </otherwise>
        </choose>

    </select>

    <select id="findTdSendSopGroups" resultType="org.scrm.domain.sop.vo.content.WeGroupSopToBeSentVo">
        SELECT
            wg.group_name,
            wg.chat_id,
            wg.add_time
        FROM
            we_sop_execute_target_attachments wseta
                LEFT JOIN we_sop_execute_target wset ON wset.id = wseta.execute_target_id
                LEFT JOIN we_group wg ON wg.chat_id = wset.target_id
                LEFT JOIN we_sop_base wsb on wsb.id=wset.sop_base_id
        WHERE
            wg.`owner` = #{weUserId} and wg.del_flag=0 and   wset.target_type = 2 and wsb.del_flag=0 and wsb.sop_state=1 and wsb.send_type=2
        <choose>
            <when test="isExpiringSoon">
                AND   timestampdiff(MINUTE,NOW(),wseta.push_end_time) BETWEEN 0 AND 10
            </when>
<!--            <otherwise>-->
<!--                AND to_days( wseta.push_start_time ) = to_days(now())-->
<!--            </otherwise>-->
        </choose>
    </select>





    <!-- 查询拨打电话SOP执行内容（匹配当前员工的客户记录） -->
    <select id="findPhoneCallSopExecuteContent" resultType="org.scrm.domain.sop.vo.content.WeCustomerSopBaseContentVo">
        SELECT
        wseca.push_time_type,
        wseca.push_time_pre,
        wseca.push_day_number,
        wseca.push_start_time,
        wseca.push_end_time,
        wseca.execute_state,
        wseca.execute_time,
        wseca.sop_attachment_id,
        wc.customer_name,
        wc.customer_type,
        wc.external_userid,
        wc.gender,
        wc.avatar,
        wc.phone,
        wset.sop_base_id,
        wsb.sop_name,
        wsb.business_type,
        wseca.id as executeTargetAttachId
        FROM
        we_sop_execute_target_attachments wseca
        LEFT JOIN we_sop_execute_target wset ON wseca.execute_target_id=wset.id
        LEFT JOIN we_sop_base wsb ON wsb.id=wset.sop_base_id
        LEFT JOIN we_customer wc ON wc.external_userid=wset.target_id AND wc.add_user_id=wset.execute_we_user_id
        WHERE
        wsb.del_flag=0
        AND wsb.sop_state=1
        AND wset.execute_we_user_id=#{executeWeUserId}
        AND wsb.business_type=#{businessType}
        -- 拨打电话SOP：不限制执行目标状态，因为即使SOP正常结束(3)，也可能需要显示历史任务
        <if test="executeSubState != null">
            AND wseca.execute_state=#{executeSubState}
        </if>
        ORDER BY wc.customer_name, wseca.push_start_time DESC
    </select>

    <!-- 查询特定拨打电话SOP执行内容（根据sopBaseId过滤，直接查询拨打状态） -->
    <select id="findPhoneCallSopExecuteContentBySopBaseId" resultType="org.scrm.domain.sop.vo.content.WeCustomerSopBaseContentVo">
        SELECT
        wseca.push_time_type,
        wseca.push_time_pre,
        wseca.push_day_number,
        wseca.push_start_time,
        wseca.push_end_time,
        wseca.execute_state,
        wseca.execute_time,
        wseca.sop_attachment_id,
        wc.customer_name,
        wc.customer_type,
        wc.external_userid,
        wc.gender,
        wc.avatar,
        wc.phone,
        wset.sop_base_id,
        wsb.sop_name,
        wsb.business_type,
        wseca.id as executeTargetAttachId,
        -- 直接查询拨打状态：如果有拨打记录则为1，否则为0
        CASE WHEN wpcr.id IS NOT NULL THEN 1 ELSE 0 END as callStatus,
        -- 查询拨打时间：获取最新的拨打记录时间
        wpcr.create_time as callTime
        FROM
        we_sop_execute_target_attachments wseca
        LEFT JOIN we_sop_execute_target wset ON wseca.execute_target_id=wset.id
        LEFT JOIN we_sop_base wsb ON wsb.id=wset.sop_base_id
        LEFT JOIN we_customer wc ON wc.external_userid=wset.target_id AND wc.add_user_id=wset.execute_we_user_id
        -- 左连接拨打记录表，按executeTargetAttachId精确匹配（修复类型匹配问题）
        LEFT JOIN we_phone_call_record wpcr ON wpcr.external_userid=wc.external_userid
                                            AND wpcr.we_user_id=wset.execute_we_user_id
                                            AND wpcr.sop_base_id=wsb.id
                                            AND wpcr.execute_target_attach_id=CAST(wseca.id AS SIGNED)
        WHERE
        wsb.del_flag=0
        AND wsb.sop_state=1
        AND wset.execute_we_user_id=#{executeWeUserId}
        AND wsb.business_type=#{businessType}
        AND wsb.id=#{sopBaseId}
        -- 拨打电话SOP：不限制执行目标状态，因为即使SOP正常结束(3)，也可能需要显示历史任务
        <if test="executeSubState != null">
            AND wseca.execute_state=#{executeSubState}
        </if>
        -- 如果指定了executeTargetAttachId，则查询该时间段的所有客户数据
        <if test="executeTargetAttachId != null and executeTargetAttachId != ''">
            AND wseca.push_start_time = (
                SELECT push_start_time
                FROM we_sop_execute_target_attachments
                WHERE id = #{executeTargetAttachId}
            )
            AND wseca.push_end_time = (
                SELECT push_end_time
                FROM we_sop_execute_target_attachments
                WHERE id = #{executeTargetAttachId}
            )
        </if>
        ORDER BY wc.customer_name, wseca.push_start_time DESC
    </select>

    <select id="findSopToBeSentContentInfo" resultType="org.scrm.domain.sop.vo.content.WeSopToBeSentContentInfoVo">
        SELECT
        wsb.id as sopBaseId,
        wsb.sop_name,
        wsb.business_type,
        wseta.push_start_time,
        wseta.push_end_time
        FROM
        we_sop_execute_target wset
        LEFT JOIN we_sop_base wsb ON wsb.id = wset.sop_base_id
        LEFT JOIN we_sop_execute_target_attachments wseta ON wseta.execute_target_id = wset.id
        WHERE  to_days(wseta.push_start_time) = to_days(now()) and  wseta.execute_state=0 and wsb.del_flag=0 and wsb.sop_state=1
        <if test="weUserId != '' and weUserId !=null">
            and  wset.execute_we_user_id=#{weUserId}
        </if>
        <if test="targetId">
            and wset.target_id=#{targetId}
        </if>
    </select>


    <update id="updateSopState">
        UPDATE we_sop_base
        SET sop_state = #{sopState}
        WHERE
            id=#{sopId}


    </update>




</mapper>
