{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753430263537}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}