2025-07-25 14:37:46.348 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 14:37:46.379 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:37:46.379 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 14:37:48.209 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:37:48.211 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:37:48.409 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 184 ms. Found 0 Redis repository interfaces.
2025-07-25 14:37:48.970 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 14:37:49.360 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$f3e9f753] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.387 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.398 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.414 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.418 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.430 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.444 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.504 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:50.001 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 14:37:50.385 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 14:37:50.396 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 14:37:50.396 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 14:37:50.397 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 14:37:50.651 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 14:37:50.651 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4271 ms
2025-07-25 14:37:51.188 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:37:51.944 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 14:37:53.207 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 14:38:01.570 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:01.581 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:02.017 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:02.772 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:03.350 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 14:38:03.682 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:38:03.689 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:38:04.225 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:04.250 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:05.550 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:08.658 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:09.634 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:10.285 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:13.228 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:13.600 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:15.495 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:15.552 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:17.053 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:17.948 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:24.061 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 14:38:24.065 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 14:38:25.970 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 14:38:26.308 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:38:26.309 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 14:38:26.310 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:38:26.618 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 14:38:26.639 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 14:38:26.644 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 14:38:26.645 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 14:38:26.674 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 14:38:26.815 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 43.017 seconds (JVM running for 46.169)
2025-07-25 14:38:26.826 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.828 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:26.829 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.829 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:26.830 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.830 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:26.830 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.830 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:28.144 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:28.857 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:43.429 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 14:38:43.429 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 14:38:43.461 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 31 ms
2025-07-25 14:38:44.173 [http-nio-6091-exec-1] INFO  o.scrm.controller.WeIndexController - 可见范围客户数:[]
2025-07-25 14:38:44.997 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5fe94164[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:38:45.301 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5991bfda[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:38:46.499 [http-nio-6091-exec-2] ERROR c.a.druid.filter.stat.StatFilter - slow sql 1633 millis. SELECT
            date as xTime,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
             AND del_flag=0
              
            ) as totalCnt,

          ((SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
            AND del_flag=0
             
            ) - (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
            AND del_flag=0
             
            )) as repeatCnt,

            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date
                 AND del_flag=0
                 
            ) as addCnt,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and track_state=5
                 
             ) as lostCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and del_flag=1
             
            ) as userDelCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date and del_flag=0 and track_state!=5
             
           ) as netCnt,
            (SELECT IFNULL(sum(new_apply_cnt),0) from we_user_behavior_data WHERE DATE_FORMAT(stat_time,'%Y-%m-%d')=date

                 
           ) as applyCnt
        FROM
        sys_dim_date
        WHERE DATE_FORMAT(date,'%Y-%m-%d') BETWEEN ? and ? 
        ORDER BY date ASC["2025-07-19","2025-07-25"]
2025-07-25 14:39:04.285 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:10.240 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:11.952 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:14.958 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@676f097d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:14.968 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@54fdfcf5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:45.281 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@480dab47[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:52.056 [http-nio-6091-exec-6] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-25 14:39:52.139 [http-nio-6091-exec-6] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#76cf849f:0/SimpleConnection@32b7ba27 [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 59816]
2025-07-25 14:39:52.232 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948634283865628673 创建成功
2025-07-25 14:39:52.413 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:58.889 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@41c651e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:59.257 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@41c651e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:59.589 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@41c651e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:08.602 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1d074577[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:08.674 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1d074577[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:08.739 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1d074577[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:35.879 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:47.804 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:50.053 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:52.069 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:54.374 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@93ef479[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:54.381 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@18619231[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:41:48.569 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948634773030526977 创建成功
2025-07-25 14:41:48.751 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:41:56.934 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750c7930[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:41:57.293 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750c7930[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:41:57.621 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750c7930[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:06.186 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1e12a139[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:06.254 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1e12a139[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:06.321 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1e12a139[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:11.749 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:42:11.775 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 原始标签数: 2, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ]
2025-07-25 14:42:12.009 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 原始标签数: 2, 待新增: 0, 待移除: 2, 有实际变更: true
2025-07-25 14:42:12.035 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:42:12.083 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@362d2868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:12.148 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@362d2868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:12.214 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1940657362893729794
2025-07-25 14:42:12.214 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@362d2868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:12.304 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1940655177334190082
2025-07-25 14:42:12.401 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943494406517850113
2025-07-25 14:42:12.495 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943512365776830465
2025-07-25 14:42:12.585 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943506653168398337
2025-07-25 14:42:12.672 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943512561625661441
2025-07-25 14:42:12.761 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943512828232400898
2025-07-25 14:42:12.851 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943532777965416450
2025-07-25 14:42:12.945 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1945496637924466689
2025-07-25 14:42:13.031 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948018399404908545
2025-07-25 14:42:13.031 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 移除数量: 10
2025-07-25 14:42:13.064 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-开始评估SOP，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:13.096 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977
2025-07-25 14:42:13.096 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-检查客户SOP条件，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 标签条件: etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, 包含关系: 2
2025-07-25 14:42:13.096 [sop-reevaluation-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4636c0b1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户SOP条件检查结果，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 符合条件: false, 查询结果数量: 0
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户未添加到SOP执行计划，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-SOP客户重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 检查SOP数: 1, 新增执行计划: 0
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:42:18.194 [http-nio-6091-exec-8] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:42:18.217 [http-nio-6091-exec-8] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 原始标签数: 0, 原始标签: []
2025-07-25 14:42:18.396 [http-nio-6091-exec-8] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 原始标签数: 0, 待新增: 2, 待移除: 0, 有实际变更: true
2025-07-25 14:42:18.417 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:42:18.468 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@457d6fae[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.485 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-开始评估SOP，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:18.507 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977
2025-07-25 14:42:18.507 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-检查客户SOP条件，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 标签条件: etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, 包含关系: 2
2025-07-25 14:42:18.508 [sop-reevaluation-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5581799c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.535 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@457d6fae[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.555 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户SOP条件检查结果，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 符合条件: true, 查询结果数量: 1
2025-07-25 14:42:18.595 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@457d6fae[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.692 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-25 14:42:18.717 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:42:18, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:42:18.717 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:42:18, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:42:18.721 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户 wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g 已添加到SOP 1948634773030526977 (0725-testA) 的执行计划中
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户成功添加到SOP执行计划，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-SOP客户重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 检查SOP数: 1, 新增执行计划: 1
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:42:51.927 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21323316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:51.994 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21323316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:52.059 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21323316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:43:57.201 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:44:00.294 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:44:02.165 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:44:03.738 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7d2224a6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:44:03.748 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@16dee87e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:44:25.069 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6886dc3a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:44:30.790 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948635453413744641 创建成功
2025-07-25 14:44:30.937 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:00.517 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:02.112 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:03.623 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:05.082 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:09.386 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@71f42b8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:09.388 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7a203fb0[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:13.398 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:16.541 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6d99a165[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:16.905 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6d99a165[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:17.245 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6d99a165[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:25.471 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@c2e0663[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:25.541 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@c2e0663[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:25.605 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@c2e0663[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:29.729 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:45:29.752 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 原始标签数: 3, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ, etwS5SKgAA0Mq46bDWAw1GopBKIr_PjQ]
2025-07-25 14:45:29.977 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 原始标签数: 3, 待新增: 0, 待移除: 3, 有实际变更: true
2025-07-25 14:45:29.997 [sop-reevaluation-3] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-3
2025-07-25 14:45:30.030 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3aafaf27[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:30.096 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3aafaf27[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:30.156 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1944946052154281986
2025-07-25 14:45:30.159 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3aafaf27[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:30.238 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1943544304063803394
2025-07-25 14:45:30.321 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1943532777965416450
2025-07-25 14:45:30.403 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1943506653168398337
2025-07-25 14:45:30.488 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1945496637924466689
2025-07-25 14:45:30.573 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948018399404908545
2025-07-25 14:45:30.573 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 移除数量: 6
2025-07-25 14:45:30.595 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo
2025-07-25 14:45:30.595 [sop-reevaluation-3] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-3
2025-07-25 14:45:34.155 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:45:34.179 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 原始标签数: 3, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ, etwS5SKgAAj5t7mr_uy2GRlHw24LHg7A]
2025-07-25 14:45:34.407 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 原始标签数: 3, 待新增: 0, 待移除: 3, 有实际变更: true
2025-07-25 14:45:34.427 [sop-reevaluation-4] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-4
2025-07-25 14:45:34.460 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ede267c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:34.526 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ede267c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:34.582 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1943506653168398337
2025-07-25 14:45:34.585 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ede267c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:34.675 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1943532777965416450
2025-07-25 14:45:34.762 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1945496637924466689
2025-07-25 14:45:34.848 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1944946052154281986
2025-07-25 14:45:34.935 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1943544304063803394
2025-07-25 14:45:35.021 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1945496637924466689
2025-07-25 14:45:35.105 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1948018399404908545
2025-07-25 14:45:35.105 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 移除数量: 7
2025-07-25 14:45:35.129 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo
2025-07-25 14:45:35.129 [sop-reevaluation-4] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-4
2025-07-25 14:45:38.980 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:45:39.001 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 原始标签数: 2, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ]
2025-07-25 14:45:39.221 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 原始标签数: 2, 待新增: 0, 待移除: 2, 有实际变更: true
2025-07-25 14:45:39.241 [sop-reevaluation-5] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-5
2025-07-25 14:45:39.273 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@205ae7f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:39.338 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@205ae7f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:39.405 [sop-reevaluation-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977
2025-07-25 14:45:39.405 [sop-reevaluation-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 移除数量: 1
2025-07-25 14:45:39.406 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@205ae7f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:39.426 [sop-reevaluation-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo
2025-07-25 14:45:39.426 [sop-reevaluation-5] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-5
2025-07-25 14:45:43.166 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:44.705 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@151d45a8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:44.705 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750b1a5a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:16.350 [http-nio-6091-exec-10] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948635896210612225 创建成功
2025-07-25 14:46:16.645 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:46:30.463 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@692956ef[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:30.800 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@692956ef[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:31.130 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@692956ef[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:39.472 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39911acc[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:39.538 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39911acc[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:39.626 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39911acc[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.609 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:46:44.635 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 原始标签数: 0, 原始标签: []
2025-07-25 14:46:44.794 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 原始标签数: 0, 待新增: 1, 待移除: 0, 有实际变更: true
2025-07-25 14:46:44.815 [sop-reevaluation-6] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-6
2025-07-25 14:46:44.848 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6f7d6881[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.898 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-开始评估SOP，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, SOP名称: A
2025-07-25 14:46:44.912 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6f7d6881[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.919 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - 新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225
2025-07-25 14:46:44.919 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-检查客户SOP条件，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, 标签条件: etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ, 包含关系: 2
2025-07-25 14:46:44.919 [sop-reevaluation-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4e8e6f54[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.967 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户SOP条件检查结果，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, 符合条件: true, 查询结果数量: 1
2025-07-25 14:46:44.979 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6f7d6881[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:45.090 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-25 14:46:45.115 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:46:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:46:45.115 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:46:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:46:45.115 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户 wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg 已添加到SOP 1948635896210612225 (A) 的执行计划中
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户成功添加到SOP执行计划，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, SOP名称: A
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-SOP客户重新评估完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 检查SOP数: 1, 新增执行计划: 1
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-6
2025-07-25 14:46:48.065 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:46:53.502 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:46:55.984 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4539aae7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:55.984 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7054a929[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:48:00.880 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:08.668 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:11.369 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:16.217 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1b945bf0[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:48:16.219 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6fe04943[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:48:48.525 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:56.831 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:49:15.003 [Thread-94] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 14:49:15.003 [Thread-94] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 14:49:15.004 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 14:49:15.004 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 14:50:31.026 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 14:50:31.076 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:50:31.077 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 14:50:31.077 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 14:50:31.078 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:50:31.079 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 14:50:31.080 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 14:50:31.080 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 14:50:34.982 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:50:34.987 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:50:35.257 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 245 ms. Found 0 Redis repository interfaces.
2025-07-25 14:50:36.033 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 14:50:36.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$d80c0547] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.797 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.802 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.812 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.828 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.830 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.846 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.866 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:37.657 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 14:50:38.252 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 14:50:38.267 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 14:50:38.268 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 14:50:38.268 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 14:50:38.587 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 14:50:38.587 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7506 ms
2025-07-25 14:50:39.312 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:40.187 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 14:50:41.704 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 14:50:54.570 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:54.585 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:55.194 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:56.477 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:57.415 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 14:50:57.894 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:50:57.906 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:50:58.591 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:58.610 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:59.434 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:00.667 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:01.161 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:01.442 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:03.377 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:03.609 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:04.609 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:04.673 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:06.003 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:06.747 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:09.248 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 14:51:09.250 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 14:51:11.102 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 14:51:11.685 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:51:11.686 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 14:51:11.687 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:51:11.961 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 14:51:11.982 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 14:51:11.988 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 14:51:11.990 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 14:51:12.027 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 14:51:12.264 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 46.106 seconds (JVM running for 50.291)
2025-07-25 14:51:12.271 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.273 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:12.274 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.275 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:12.275 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.275 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:12.276 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.276 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:13.047 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:51:13.055 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:51:59.003 [http-nio-6091-exec-4] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 14:51:59.003 [http-nio-6091-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 14:51:59.009 [http-nio-6091-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-25 14:51:59.674 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:52:02.917 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:52:04.725 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@60e7c658[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:04.727 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@52e300df[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:08.348 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4cfb2fa9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:08.735 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4cfb2fa9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:09.068 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4cfb2fa9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:19.466 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@32e31e17[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:19.542 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@32e31e17[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:19.601 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@32e31e17[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:26.035 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:52:26.062 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 原始标签数: 0, 原始标签: []
2025-07-25 14:52:26.230 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 原始标签数: 0, 待新增: 1, 待移除: 0, 有实际变更: true
2025-07-25 14:52:26.254 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:52:26.300 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70f5fb8d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:26.345 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo
2025-07-25 14:52:26.346 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:52:26.369 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70f5fb8d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:26.430 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70f5fb8d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:31.330 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:52:31.351 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 原始标签数: 1, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw]
2025-07-25 14:52:31.518 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 原始标签数: 1, 待新增: 2, 待移除: 0, 有实际变更: true
2025-07-25 14:52:31.537 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:52:31.581 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5177d6b3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:31.604 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo
2025-07-25 14:52:31.604 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:52:31.659 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5177d6b3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:31.724 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5177d6b3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:34.394 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:52:36.439 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@16798953[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:36.439 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@46fa064[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:53:08.973 [http-nio-6091-exec-4] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-25 14:53:09.034 [http-nio-6091-exec-4] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#52b4da7c:0/SimpleConnection@5afb8c3f [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 61655]
2025-07-25 14:53:09.119 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948637626549792770 创建成功
2025-07-25 14:53:09.303 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:53:17.535 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:54:24.457 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:54:28.081 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@18c3224e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:54:28.081 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4f1ef0a6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:54:35.132 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@35a98f23[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:54:37.618 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948637998660055042 创建成功
2025-07-25 14:54:37.865 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:55:56.942 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:55:58.611 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:02.412 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:04.165 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:06.342 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@47d8943d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:06.343 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@56b20343[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:30.645 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21f75419[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:35.861 [http-nio-6091-exec-10] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948638494602948609 创建成功
2025-07-25 14:56:36.344 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:47.307 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:48.806 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7d653b2[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:48.806 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@384ef96e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:54.841 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5ba27316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:57.746 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:57:05.294 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:57:07.251 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2db11cf8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:07.251 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3a2d93f4[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:12.531 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@65cb936[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:44.154 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7f2c4b64[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:44.156 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3ffb1d62[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:48.591 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:57:50.619 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@694e64e9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:50.619 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7513ec13[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:03.888 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2ef2bb3e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:23.927 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4aaf61e5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:23.927 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@25ceaf2c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:25.699 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:58:28.832 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:58:30.799 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70684f5e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:30.799 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5c4fc210[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:36.029 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@74ef00e7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:52.197 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:59:49.790 [Thread-88] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 14:59:49.790 [Thread-88] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 14:59:49.791 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 14:59:49.792 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:15:35.421 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 15:15:35.447 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:15:37.396 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:15:37.399 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:15:37.673 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 257 ms. Found 0 Redis repository interfaces.
2025-07-25 15:15:38.301 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 15:15:38.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$6b48b07] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.906 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.919 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.922 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.928 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/110238970] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.956 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.969 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:39.024 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:39.039 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:39.511 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:15:39.962 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 15:15:39.972 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 15:15:39.972 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:15:39.973 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 15:15:40.218 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:15:40.218 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4770 ms
2025-07-25 15:15:40.938 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:41.650 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:15:43.039 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:15:52.295 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:52.308 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:52.711 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:53.562 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:54.183 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 15:15:54.580 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:15:54.590 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:15:54.958 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:54.973 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:55.618 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:57.097 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:57.603 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:57.920 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:00.433 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:00.720 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:01.829 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:01.878 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:03.178 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:03.997 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:05.259 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:16:05.260 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:16:07.010 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:16:07.558 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:16:07.559 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:16:07.560 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:16:07.828 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 15:16:07.856 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 15:16:07.863 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:16:07.864 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:16:07.902 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 15:16:08.617 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 35.509 seconds (JVM running for 37.42)
2025-07-25 15:16:08.628 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.631 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.633 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.633 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.633 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.634 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.634 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.634 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.894 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:16:08.902 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:17:01.405 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:17:01.405 [Thread-82] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:17:01.405 [Thread-82] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:17:01.406 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:19:01.244 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:19:03.274 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:19:03.277 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:19:03.455 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 0 Redis repository interfaces.
2025-07-25 15:19:04.028 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 15:19:04.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$b84f11c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.569 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.573 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.579 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.587 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.590 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.592 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/499052031] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.616 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.680 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.693 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:05.180 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:19:05.586 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 15:19:05.596 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 15:19:05.596 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:19:05.596 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 15:19:05.862 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:19:05.862 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4591 ms
2025-07-25 15:19:06.486 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:07.070 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:19:08.379 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:19:17.306 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:17.316 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:17.676 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:19.398 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:19.920 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 15:19:20.194 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:19:20.202 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:19:20.459 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:20.472 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:20.977 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:22.059 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:22.468 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:22.679 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:24.250 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:24.460 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:25.216 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:25.258 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:26.284 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:26.863 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:27.938 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:19:27.939 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:19:28.761 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:19:28.963 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:19:28.964 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:19:28.965 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:19:29.167 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 15:19:29.180 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 15:19:29.183 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:19:29.184 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:19:29.213 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 15:19:29.354 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 30.936 seconds (JVM running for 33.326)
2025-07-25 15:19:29.360 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.361 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:29.362 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.362 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:29.362 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.362 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:29.363 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.363 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:30.261 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:30.268 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:21:13.360 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:21:13.360 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:21:13.366 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-25 15:21:13.503 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:21:16.453 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ca9fa1f[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:21:16.460 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3ec5021b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:21:49.776 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:21:59.153 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@59110ac4[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:21:59.161 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7debcb6e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:23:02.723 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:23:47.802 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:31:17.523 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:31:17.523 [Thread-74] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:31:17.524 [Thread-74] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:31:17.524 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:33:09.166 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 15:33:09.184 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:33:10.932 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:33:10.934 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:33:11.103 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 156 ms. Found 0 Redis repository interfaces.
2025-07-25 15:33:11.557 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 15:33:11.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$dc4ae769] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.982 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.989 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.992 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.999 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.010 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.059 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.073 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.461 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:33:12.807 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 15:33:12.817 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 15:33:12.818 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:33:12.818 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 15:33:13.093 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:33:13.094 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3909 ms
2025-07-25 15:33:13.626 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:14.162 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:33:15.384 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:33:24.043 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:24.054 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:24.387 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:25.125 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:25.649 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 15:33:25.956 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:33:25.964 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:33:26.253 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:26.266 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:26.849 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:27.859 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:28.329 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:28.576 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:30.224 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:30.403 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:31.259 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:31.318 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:32.358 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:33.075 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:34.280 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:33:34.281 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:33:35.108 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:33:35.328 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:33:35.328 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:33:35.329 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:33:35.534 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 15:33:35.546 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 15:33:35.550 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:33:35.551 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:33:35.582 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 15:33:35.727 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 28.444 seconds (JVM running for 29.966)
2025-07-25 15:33:35.733 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.734 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:35.735 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.735 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:35.735 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.735 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:35.736 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.736 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:36.620 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:33:36.626 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:35:21.944 [http-nio-6091-exec-2] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:35:21.944 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:35:21.948 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-25 15:35:22.087 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:35:35.002 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:35:36.922 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@704bf070[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:36.922 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2ddd855e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:41.056 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7e8a17f3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:47.707 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3056f223[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:50.771 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:35:53.288 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2808b7d1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:53.288 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@31552536[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:36:06.037 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:36:06.037 [Thread-70] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:36:06.037 [Thread-70] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:36:06.038 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:36:06.203 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 15:36:06.204 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-web-api:**************:6091 from beat map.
2025-07-25 15:36:06.204 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 15:36:06.239 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 15:36:06.239 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-25 15:36:06.856 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-25 15:36:06.857 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-07-25 16:28:16.805 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:28:16.824 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:28:18.412 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:28:18.414 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:28:18.604 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 179 ms. Found 0 Redis repository interfaces.
2025-07-25 16:28:19.011 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:28:19.376 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$54a15df6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.402 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.408 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.411 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.417 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.423 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.433 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.483 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.495 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:28:19.870 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:28:20.185 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:28:20.193 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:28:20.193 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:28:20.193 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:28:20.430 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:28:20.431 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3607 ms
2025-07-25 16:28:20.895 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:28:21.383 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:28:22.852 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:28:28.854 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:28:28.864 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:28:29.166 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:28:29.876 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:28:30.487 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:28:32.805 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentController': Unsatisfied dependency expressed through field 'weAgentInfoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentInfoServiceImpl': Unsatisfied dependency expressed through field 'weMaterialService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
2025-07-25 16:28:32.820 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-25 16:28:32.824 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-25 16:28:32.827 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-25 16:28:32.836 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-25 16:28:32.859 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentController': Unsatisfied dependency expressed through field 'weAgentInfoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentInfoServiceImpl': Unsatisfied dependency expressed through field 'weMaterialService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:953)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:740)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:415)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.scrm.ScrmWeApiApplication.main(ScrmWeApiApplication.java:24)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentInfoServiceImpl': Unsatisfied dependency expressed through field 'weMaterialService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 18 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 32 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 59 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 78 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 91 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$0(ConnectionPool.java:154)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:318)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$6(ConnectionPool.java:277)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at org.redisson.client.RedisClient$1$2.run(RedisClient.java:257)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:469)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: 127.0.0.1/127.0.0.1:6379
	at java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:326)
	at java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:338)
	at java.util.concurrent.CompletableFuture.uniRelay(CompletableFuture.java:911)
	at java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:899)
	... 11 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: 127.0.0.1/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-07-25 16:28:33.813 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:29:12.608 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:29:12.629 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:29:12.629 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:29:12.630 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:29:12.630 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:29:12.630 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:29:12.630 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:29:12.630 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:29:14.226 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:29:14.230 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:29:14.549 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 307 ms. Found 0 Redis repository interfaces.
2025-07-25 16:29:15.340 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:29:16.036 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$73852434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.088 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.102 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.105 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.115 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.126 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.128 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.130 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.142 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.169 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.240 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:29:16.799 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:29:17.352 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:29:17.365 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:29:17.365 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:29:17.366 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:29:17.637 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:29:17.638 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5007 ms
2025-07-25 16:29:18.671 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:21.242 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:29:24.020 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:29:31.093 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:31.103 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:31.409 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:32.087 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:32.627 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:29:32.904 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:29:32.911 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:29:33.182 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:33.194 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:33.687 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:34.640 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:35.073 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:35.337 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:36.862 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:37.036 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:37.784 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:37.825 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:38.747 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:39.338 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:29:40.346 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 16:29:40.347 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 16:29:41.181 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 16:29:41.383 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:29:41.384 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 16:29:41.385 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:29:41.592 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 16:29:41.607 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 16:29:41.611 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:29:41.613 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:29:41.646 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 16:29:41.788 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 30.918 seconds (JVM running for 32.448)
2025-07-25 16:29:41.793 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:29:41.794 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:29:41.795 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:29:41.796 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:29:41.796 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:29:41.796 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:29:41.797 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:29:41.797 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:29:42.749 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 16:29:42.757 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 16:29:53.634 [http-nio-6091-exec-2] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:29:53.634 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 16:29:53.638 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-25 16:29:59.769 [http-nio-6091-exec-2] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 1, sopBaseId: '1948650536413155330', executeTargetAttachId: '1948650537071595522'
2025-07-25 16:30:00.609 [http-nio-6091-exec-2] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1948650536413155330, executeTargetAttachId: 1948650537071595522
2025-07-25 16:30:04.420 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1948650536413155330, 执行目标附件ID: 1948650537071595522
2025-07-25 16:30:07.837 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1948650536413155330
2025-07-25 16:30:13.199 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 未找到SOP基础信息，sopBaseId: 1948650536413155330
2025-07-25 16:31:17.962 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@scrm-web-api', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:31:19.372 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 16:31:19.822 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> []
2025-07-25 16:31:30.651 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 16:31:30.654 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 16:31:30.717 [http-nio-6091-exec-2] ERROR org.scrm.controller.WeSopController - 查询客户SOP内容失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'iyque-dev.we_phone_call_record' doesn't exist
### The error may exist in file [D:\project\scrm\api\scrm-module\scrm-business\target\classes\mapper\business\WeSopBaseMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT         wseca.push_time_type,         wseca.push_time_pre,         wseca.push_day_number,         wseca.push_start_time,         wseca.push_end_time,         wseca.execute_state,         wseca.execute_time,         wseca.sop_attachment_id,         wc.customer_name,         wc.customer_type,         wc.external_userid,         wc.gender,         wc.avatar,         wc.phone,         wset.sop_base_id,         wsb.sop_name,         wsb.business_type,         wseca.id as executeTargetAttachId,         -- 直接查询拨打状态：如果有拨打记录则为1，否则为0         CASE WHEN wpcr.id IS NOT NULL THEN 1 ELSE 0 END as callStatus,         -- 查询拨打时间：获取最新的拨打记录时间         wpcr.create_time as callTime         FROM         we_sop_execute_target_attachments wseca         LEFT JOIN we_sop_execute_target wset ON wseca.execute_target_id=wset.id         LEFT JOIN we_sop_base wsb ON wsb.id=wset.sop_base_id         LEFT JOIN we_customer wc ON wc.external_userid=wset.target_id AND wc.add_user_id=wset.execute_we_user_id         -- 左连接拨打记录表，按executeTargetAttachId精确匹配（修复类型匹配问题）         LEFT JOIN we_phone_call_record wpcr ON wpcr.external_userid=wc.external_userid                                             AND wpcr.we_user_id=wset.execute_we_user_id                                             AND wpcr.sop_base_id=wsb.id                                             AND wpcr.execute_target_attach_id=CAST(wseca.id AS SIGNED)         WHERE         wsb.del_flag=0         AND wsb.sop_state=1         AND wset.execute_we_user_id=?         AND wsb.business_type=?         AND wsb.id=?         -- 拨打电话SOP：不限制执行目标状态，因为即使SOP正常结束(3)，也可能需要显示历史任务                   -- 如果指定了executeTargetAttachId，则查询该时间段的所有客户数据                       AND wseca.push_start_time = (                 SELECT push_start_time                 FROM we_sop_execute_target_attachments                 WHERE id = ?             )             AND wseca.push_end_time = (                 SELECT push_end_time                 FROM we_sop_execute_target_attachments                 WHERE id = ?             )                   ORDER BY wpcr.create_time,wc.customer_name, wseca.push_start_time DESC
### Cause: java.sql.SQLSyntaxErrorException: Table 'iyque-dev.we_phone_call_record' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'iyque-dev.we_phone_call_record' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy155.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy482.findPhoneCallSopExecuteContentBySopBaseId(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy483.findPhoneCallSopExecuteContentBySopBaseId(Unknown Source)
	at org.scrm.service.impl.WeSopBaseServiceImpl.findPhoneCallSopContentBySopBaseId(WeSopBaseServiceImpl.java:1261)
	at org.scrm.service.impl.WeSopBaseServiceImpl$$FastClassBySpringCGLIB$$df7decfa.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at org.scrm.service.impl.WeSopBaseServiceImpl$$EnhancerBySpringCGLIB$$a5047fbf.findPhoneCallSopContentBySopBaseId(<generated>)
	at org.scrm.controller.WeSopController.findCustomerSopContent(WeSopController.java:109)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.scrm.base.filter.TraceIdFilter.doFilterInternal(TraceIdFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Table 'iyque-dev.we_phone_call_record' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy729.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy727.query(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:132)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy726.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:64)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy726.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 78 common frames omitted
2025-07-25 16:32:05.869 [Thread-70] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 16:32:05.869 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:32:05.869 [Thread-70] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 16:32:05.869 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 16:33:11.971 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:33:11.989 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:33:11.990 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:33:11.990 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:33:11.990 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:33:11.990 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:33:11.990 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:33:11.990 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:33:13.702 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:33:13.706 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:33:13.925 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 201 ms. Found 0 Redis repository interfaces.
2025-07-25 16:33:14.424 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:33:14.901 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$cfdcfeea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.929 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.946 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.960 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.966 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.968 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:14.978 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:15.004 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:15.067 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:15.079 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:33:15.539 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:33:15.900 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:33:15.909 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:33:15.909 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:33:15.910 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:33:16.203 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:33:16.203 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4213 ms
2025-07-25 16:33:16.893 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:17.711 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:33:19.101 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:33:25.312 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:25.321 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:25.629 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:26.277 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:26.822 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:33:27.113 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:33:27.121 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:33:27.379 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:27.391 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:27.887 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:28.921 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:29.476 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:29.700 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:31.193 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:31.364 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:32.082 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:32.124 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:33.057 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:33.633 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:33:34.671 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 16:33:34.672 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 16:33:35.448 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 16:33:35.645 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:33:35.645 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 16:33:35.646 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:33:35.924 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 16:33:35.934 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 16:33:35.937 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:33:35.938 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:33:35.974 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 16:33:36.132 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 26.13 seconds (JVM running for 27.553)
2025-07-25 16:33:36.138 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:33:36.140 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:33:36.141 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:33:36.141 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:33:36.141 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:33:36.141 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:33:36.142 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:33:36.142 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:33:37.027 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-25 16:33:37.037 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-25 16:33:58.561 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:33:58.561 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 16:33:58.566 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-25 16:33:58.609 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 1, sopBaseId: '1948650536413155330', executeTargetAttachId: '1948650537071595522'
2025-07-25 16:33:58.609 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1948650536413155330, executeTargetAttachId: 1948650537071595522
2025-07-25 16:33:58.616 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1948650536413155330, 执行目标附件ID: 1948650537071595522
2025-07-25 16:33:58.617 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1948650536413155330
2025-07-25 16:33:58.775 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1948650536413155330, 名称: 'A', 业务类型: 7, 状态: 1
2025-07-25 16:33:58.833 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-25 16:33:58.833 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-25 16:33:58.834 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537075789826
2025-07-25 16:33:58.834 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537071595522
2025-07-25 16:33:58.835 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-25 16:33:58.835 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-25 16:33:58.835 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-25 16:33:58.835 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1948650536413155330) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-25 16:33:58.836 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-25 16:33:58.837 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-25 16:33:58.837 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-25 16:33:58.839 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-25 16:33:58.839 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:33:58.870 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537075789826, callStatus=1 (已拨打)
2025-07-25 16:33:58.872 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-25 16:33:58.872 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-25 16:33:58.872 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:33:58.904 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '干啥' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537071595522, callStatus=1 (已拨打)
2025-07-25 16:33:58.904 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 电话: 15659819768, SOP内容数量: 1
2025-07-25 16:33:58.904 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回2个客户的特定拨打电话SOP任务
2025-07-25 16:33:58.904 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='A', 拨打状态=0
2025-07-25 16:33:58.904 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='干啥', 电话='15659819768', 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', SOP名称='A', 拨打状态=0
2025-07-25 16:34:11.432 [http-nio-6091-exec-2] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 1, sopBaseId: '1948650536413155330', executeTargetAttachId: '1948650537071595522'
2025-07-25 16:34:11.806 [http-nio-6091-exec-2] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1948650536413155330, executeTargetAttachId: 1948650537071595522
2025-07-25 16:34:13.233 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1948650536413155330, 执行目标附件ID: 1948650537071595522
2025-07-25 16:34:19.544 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1948650536413155330
2025-07-25 16:34:21.838 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1948650536413155330, 名称: 'A', 业务类型: 7, 状态: 1
2025-07-25 16:35:32.887 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-25 16:35:32.887 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-25 16:35:32.887 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537075789826
2025-07-25 16:35:32.892 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537071595522
2025-07-25 16:35:32.892 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-25 16:35:32.892 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-25 16:35:32.893 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-25 16:35:32.893 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1948650536413155330) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-25 16:35:32.893 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-25 16:35:32.893 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-25 16:35:32.893 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-25 16:35:32.894 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-25 16:35:32.894 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:35:32.928 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@scrm-web-api', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:35:32.942 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537075789826, callStatus=1 (已拨打)
2025-07-25 16:35:32.942 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-25 16:35:32.942 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-25 16:35:32.943 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:35:32.962 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-25 16:35:32.963 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> []
2025-07-25 16:35:32.964 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '干啥' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537071595522, callStatus=1 (已拨打)
2025-07-25 16:35:32.964 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 电话: 15659819768, SOP内容数量: 1
2025-07-25 16:35:32.964 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回2个客户的特定拨打电话SOP任务
2025-07-25 16:35:32.964 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='A', 拨打状态=0
2025-07-25 16:35:32.964 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='干啥', 电话='15659819768', 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', SOP名称='A', 拨打状态=0
2025-07-25 16:35:34.999 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-25 16:35:35.001 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-25 16:35:57.962 [Thread-65] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 16:35:57.962 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:35:57.962 [Thread-65] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 16:35:57.963 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 16:41:15.395 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:41:15.415 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:41:16.968 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:41:16.970 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:41:17.118 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 137 ms. Found 0 Redis repository interfaces.
2025-07-25 16:41:17.521 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:41:17.862 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$fe13d9f9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.885 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.892 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.895 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.899 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.906 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.908 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.909 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.916 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.926 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.966 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:17.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:41:18.348 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:41:18.665 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:41:18.672 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:41:18.673 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:41:18.673 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:41:18.908 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:41:18.908 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3492 ms
2025-07-25 16:41:19.369 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:19.853 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:41:21.329 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:41:27.391 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:27.400 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:27.707 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:28.374 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:28.922 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:41:29.202 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:41:29.210 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:41:29.486 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:29.498 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:29.995 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:31.030 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:31.473 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:31.694 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:33.350 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:33.542 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:34.270 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:34.313 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:35.282 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:35.893 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:41:36.951 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 16:41:36.952 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 16:41:37.785 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 16:41:37.990 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:41:37.990 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 16:41:37.991 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:41:38.210 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 16:41:38.221 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 16:41:38.224 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:41:38.225 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:41:38.258 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 16:41:38.399 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 24.683 seconds (JVM running for 26.218)
2025-07-25 16:41:38.405 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:41:38.406 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:41:38.407 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:41:38.407 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:41:38.408 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:41:38.408 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:41:38.409 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:41:38.409 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:41:39.290 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 16:41:39.296 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 16:41:42.181 [http-nio-6091-exec-5] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:41:42.181 [http-nio-6091-exec-5] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 16:41:42.185 [http-nio-6091-exec-5] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-25 16:42:18.717 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:42:18.717 [Thread-63] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 16:42:18.718 [Thread-63] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 16:42:18.718 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 16:42:28.414 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:42:28.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:42:29.966 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:42:29.969 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:42:30.119 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 139 ms. Found 0 Redis repository interfaces.
2025-07-25 16:42:30.532 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:42:30.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$4b7205] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.897 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.904 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.906 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.911 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.920 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.921 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/159910421] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.928 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.936 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.976 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:30.988 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:42:31.348 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:42:31.664 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:42:31.672 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:42:31.672 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:42:31.672 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:42:31.901 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:42:31.902 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3470 ms
2025-07-25 16:42:32.384 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:32.909 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:42:34.331 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:42:40.629 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:40.638 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:40.947 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:41.599 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:42.098 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:42:42.373 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:42:42.405 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:42:42.671 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:42.683 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:43.187 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:44.187 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:44.609 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:44.857 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:46.499 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:46.667 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:47.375 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:47.416 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:48.361 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:48.948 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:42:49.993 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 16:42:49.994 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 16:42:50.807 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 16:42:51.006 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:42:51.006 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 16:42:51.007 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:42:51.260 [main] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:42:51.265 [main] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:42:51.267 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 16:42:51.277 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 16:42:51.279 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:42:51.280 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:42:51.314 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 16:42:51.453 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 24.787 seconds (JVM running for 26.413)
2025-07-25 16:42:51.458 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:42:51.459 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:42:51.460 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:42:51.460 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:42:51.461 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:42:51.461 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:42:51.461 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:42:51.461 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:42:52.355 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:42:52.355 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - modified ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:42:52.357 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:43:02.086 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:43:02.086 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 16:43:02.088 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-25 16:43:04.711 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 1, sopBaseId: '1948650536413155330', executeTargetAttachId: '1948650537071595522'
2025-07-25 16:43:04.711 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1948650536413155330, executeTargetAttachId: 1948650537071595522
2025-07-25 16:43:06.525 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1948650536413155330, 执行目标附件ID: 1948650537071595522
2025-07-25 16:43:06.525 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1948650536413155330
2025-07-25 16:43:07.170 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1948650536413155330, 名称: 'A', 业务类型: 7, 状态: 1
2025-07-25 16:43:07.297 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-25 16:43:07.298 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-25 16:43:07.298 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537071595522
2025-07-25 16:43:07.299 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537075789826
2025-07-25 16:43:07.299 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-25 16:43:07.315 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-25 16:43:07.315 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-25 16:43:07.315 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1948650536413155330) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-25 16:43:07.316 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-25 16:43:07.316 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-25 16:43:07.316 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-25 16:43:07.316 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-25 16:43:07.316 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:43:07.359 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537075789826, callStatus=1 (已拨打)
2025-07-25 16:43:07.360 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-25 16:43:07.360 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-25 16:43:07.361 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:43:07.386 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '干啥' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537071595522, callStatus=1 (已拨打)
2025-07-25 16:43:07.386 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 电话: 15659819768, SOP内容数量: 1
2025-07-25 16:43:07.386 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回2个客户的特定拨打电话SOP任务
2025-07-25 16:43:07.387 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='A', 拨打状态=0
2025-07-25 16:43:07.387 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='干啥', 电话='15659819768', 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', SOP名称='A', 拨打状态=0
2025-07-25 16:43:29.895 [http-nio-6091-exec-2] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 1, sopBaseId: '1948650536413155330', executeTargetAttachId: '1948650537071595522'
2025-07-25 16:43:32.546 [http-nio-6091-exec-2] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1948650536413155330, executeTargetAttachId: 1948650537071595522
2025-07-25 16:43:33.818 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1948650536413155330, 执行目标附件ID: 1948650537071595522
2025-07-25 16:43:34.761 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1948650536413155330
2025-07-25 16:43:36.039 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1948650536413155330, 名称: 'A', 业务类型: 7, 状态: 1
2025-07-25 16:43:54.071 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-25 16:43:54.923 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:43:54.924 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - modified ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:43:56.928 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:43:56.928 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-25 16:43:57.553 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537071595522
2025-07-25 16:43:57.554 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537075789826
2025-07-25 16:44:32.998 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-25 16:44:32.998 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-25 16:44:32.999 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-25 16:44:33.000 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1948650536413155330) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-25 16:44:33.000 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-25 16:44:33.000 [http-nio-6091-exec-2] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-25 16:44:33.000 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-25 16:44:33.000 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-25 16:44:33.000 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:44:33.026 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537075789826, callStatus=1 (已拨打)
2025-07-25 16:44:33.026 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-25 16:44:33.026 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-25 16:44:33.026 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:44:33.030 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@scrm-web-api', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:44:33.038 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:44:33.040 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> []
2025-07-25 16:44:33.051 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '干啥' 时间段 Fri Jul 25 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1948650537071595522, callStatus=1 (已拨打)
2025-07-25 16:44:33.051 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 电话: 15659819768, SOP内容数量: 1
2025-07-25 16:44:33.051 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回2个客户的特定拨打电话SOP任务
2025-07-25 16:44:33.051 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='A', 拨打状态=0
2025-07-25 16:44:33.051 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='干啥', 电话='15659819768', 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', SOP名称='A', 拨打状态=0
2025-07-25 16:44:33.054 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:44:33.055 [Thread-63] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 16:44:33.056 [Thread-63] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 16:44:33.056 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 16:46:06.473 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:46:06.495 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:46:08.119 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:46:08.122 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:46:08.286 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 146 ms. Found 0 Redis repository interfaces.
2025-07-25 16:46:08.703 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:46:09.042 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$790eafa4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.067 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.075 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.077 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.082 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.089 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.091 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.092 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.099 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.111 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.165 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:46:09.546 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:46:09.858 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:46:09.866 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:46:09.867 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:46:09.867 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:46:10.105 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:46:10.106 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3610 ms
2025-07-25 16:46:10.563 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:11.061 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:46:12.335 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:46:18.806 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:18.816 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:19.143 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:19.901 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:20.406 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:46:20.690 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:46:20.698 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:46:20.953 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:20.965 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:21.495 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:22.413 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:22.811 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:23.037 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:24.670 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:24.842 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:25.609 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:25.657 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:26.640 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:27.234 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:46:28.277 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 16:46:28.278 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 16:46:29.136 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 16:46:29.338 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:46:29.339 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 16:46:29.339 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:46:29.539 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 16:46:29.551 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 16:46:29.554 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:46:29.554 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:46:29.583 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 16:46:29.722 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 25.924 seconds (JVM running for 28.92)
2025-07-25 16:46:29.727 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:46:29.728 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:46:29.729 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:46:29.729 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:46:29.730 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:46:29.730 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:46:29.730 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:46:29.730 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:46:30.665 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 16:46:30.673 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 16:46:35.281 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:46:35.281 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 16:46:35.284 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-25 16:46:38.608 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 1, sopBaseId: '1948650536413155330', executeTargetAttachId: '1948650537071595522'
2025-07-25 16:46:38.609 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1948650536413155330, executeTargetAttachId: 1948650537071595522
2025-07-25 16:46:39.964 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1948650536413155330, 执行目标附件ID: 1948650537071595522
2025-07-25 16:46:39.964 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1948650536413155330
2025-07-25 16:46:40.397 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1948650536413155330, 名称: 'A', 业务类型: 7, 状态: 1
2025-07-25 16:46:40.476 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-25 16:46:40.477 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-25 16:46:40.477 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537071595522
2025-07-25 16:46:40.477 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1948650536413155330, SOP名称: 'A', 执行状态: 1, 附件ID: 1948650537075789826
2025-07-25 16:46:40.478 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-25 16:46:40.478 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-25 16:46:40.478 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-25 16:46:40.478 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1948650536413155330) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-25 16:46:40.479 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-25 16:46:40.479 [http-nio-6091-exec-1] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-25 16:46:40.479 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-25 16:46:40.479 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-25 16:46:40.480 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:46:40.526 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-25 16:46:40.526 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1948650536413155330, 时间段数量=1
2025-07-25 16:54:54.870 [Thread-64] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 16:54:54.870 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:54:54.870 [Thread-64] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 16:54:54.871 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 16:55:25.392 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 16:55:25.412 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 16:55:26.963 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 16:55:26.965 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 16:55:27.116 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 139 ms. Found 0 Redis repository interfaces.
2025-07-25 16:55:27.546 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 16:55:27.898 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$3546b080] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.922 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.930 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.932 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.944 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.945 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.946 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.953 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:27.962 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:28.006 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:28.018 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 16:55:28.400 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 16:55:28.742 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 16:55:28.752 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 16:55:28.753 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 16:55:28.753 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 16:55:29.002 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 16:55:29.003 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3591 ms
2025-07-25 16:55:29.484 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:29.979 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 16:55:31.456 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 16:55:37.635 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:37.644 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:37.956 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:38.617 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:39.114 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 16:55:39.410 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:55:39.419 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 16:55:39.673 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:39.685 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:40.282 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:41.217 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:41.625 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:41.849 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:43.416 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:43.581 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:44.287 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:44.329 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:45.289 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:45.923 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 16:55:46.956 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 16:55:46.958 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 16:55:47.738 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 16:55:47.949 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:55:47.950 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 16:55:47.950 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 16:55:48.196 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 16:55:48.206 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 16:55:48.209 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 16:55:48.210 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:55:48.246 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 16:55:48.398 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 24.736 seconds (JVM running for 26.09)
2025-07-25 16:55:48.404 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:55:48.406 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:55:48.407 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:55:48.407 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:55:48.407 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:55:48.407 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:55:48.408 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 16:55:48.408 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 16:55:49.267 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:55:49.272 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 16:56:26.544 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:56:26.544 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 16:56:26.547 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-25 16:57:47.642 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@scrm-web-api', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 16:57:47.665 [Thread-63] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 16:57:47.665 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 16:57:47.665 [Thread-63] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 16:57:47.667 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
