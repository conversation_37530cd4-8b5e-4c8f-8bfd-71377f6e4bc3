2025-07-25 14:37:46.348 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 14:37:46.379 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:37:46.379 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 14:37:46.380 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 14:37:48.209 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:37:48.211 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:37:48.409 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 184 ms. Found 0 Redis repository interfaces.
2025-07-25 14:37:48.970 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 14:37:49.360 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$f3e9f753] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.387 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.398 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.414 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.418 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.430 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.444 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.504 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:49.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:37:50.001 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 14:37:50.385 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 14:37:50.396 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 14:37:50.396 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 14:37:50.397 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 14:37:50.651 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 14:37:50.651 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4271 ms
2025-07-25 14:37:51.188 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:37:51.944 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 14:37:53.207 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 14:38:01.570 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:01.581 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:02.017 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:02.772 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:03.350 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 14:38:03.682 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:38:03.689 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:38:04.225 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:04.250 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:05.550 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:08.658 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:09.634 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:10.285 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:13.228 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:13.600 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:15.495 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:15.552 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:17.053 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:17.948 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:38:24.061 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 14:38:24.065 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 14:38:25.970 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 14:38:26.308 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:38:26.309 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 14:38:26.310 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:38:26.618 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 14:38:26.639 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 14:38:26.644 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 14:38:26.645 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 14:38:26.674 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 14:38:26.815 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 43.017 seconds (JVM running for 46.169)
2025-07-25 14:38:26.826 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.828 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:26.829 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.829 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:26.830 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.830 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:26.830 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:38:26.830 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:38:28.144 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:28.857 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:43.429 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 14:38:43.429 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 14:38:43.461 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 31 ms
2025-07-25 14:38:44.173 [http-nio-6091-exec-1] INFO  o.scrm.controller.WeIndexController - 可见范围客户数:[]
2025-07-25 14:38:44.997 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5fe94164[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:38:45.301 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5991bfda[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:38:46.499 [http-nio-6091-exec-2] ERROR c.a.druid.filter.stat.StatFilter - slow sql 1633 millis. SELECT
            date as xTime,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
             AND del_flag=0
              
            ) as totalCnt,

          ((SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
            AND del_flag=0
             
            ) - (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
            AND del_flag=0
             
            )) as repeatCnt,

            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date
                 AND del_flag=0
                 
            ) as addCnt,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and track_state=5
                 
             ) as lostCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and del_flag=1
             
            ) as userDelCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date and del_flag=0 and track_state!=5
             
           ) as netCnt,
            (SELECT IFNULL(sum(new_apply_cnt),0) from we_user_behavior_data WHERE DATE_FORMAT(stat_time,'%Y-%m-%d')=date

                 
           ) as applyCnt
        FROM
        sys_dim_date
        WHERE DATE_FORMAT(date,'%Y-%m-%d') BETWEEN ? and ? 
        ORDER BY date ASC["2025-07-19","2025-07-25"]
2025-07-25 14:39:04.285 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:10.240 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:11.952 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:14.958 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@676f097d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:14.968 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@54fdfcf5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:45.281 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@480dab47[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:52.056 [http-nio-6091-exec-6] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-25 14:39:52.139 [http-nio-6091-exec-6] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#76cf849f:0/SimpleConnection@32b7ba27 [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 59816]
2025-07-25 14:39:52.232 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948634283865628673 创建成功
2025-07-25 14:39:52.413 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:39:58.889 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@41c651e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:59.257 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@41c651e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:39:59.589 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@41c651e8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:08.602 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1d074577[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:08.674 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1d074577[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:08.739 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1d074577[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:35.879 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:47.804 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:50.053 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:52.069 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:40:54.374 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@93ef479[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:40:54.381 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@18619231[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:41:48.569 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948634773030526977 创建成功
2025-07-25 14:41:48.751 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:41:56.934 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750c7930[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:41:57.293 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750c7930[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:41:57.621 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750c7930[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:06.186 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1e12a139[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:06.254 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1e12a139[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:06.321 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1e12a139[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:11.749 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:42:11.775 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 原始标签数: 2, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ]
2025-07-25 14:42:12.009 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 原始标签数: 2, 待新增: 0, 待移除: 2, 有实际变更: true
2025-07-25 14:42:12.035 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:42:12.083 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@362d2868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:12.148 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@362d2868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:12.214 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1940657362893729794
2025-07-25 14:42:12.214 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@362d2868[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:12.304 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1940655177334190082
2025-07-25 14:42:12.401 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943494406517850113
2025-07-25 14:42:12.495 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943512365776830465
2025-07-25 14:42:12.585 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943506653168398337
2025-07-25 14:42:12.672 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943512561625661441
2025-07-25 14:42:12.761 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943512828232400898
2025-07-25 14:42:12.851 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1943532777965416450
2025-07-25 14:42:12.945 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1945496637924466689
2025-07-25 14:42:13.031 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948018399404908545
2025-07-25 14:42:13.031 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 移除数量: 10
2025-07-25 14:42:13.064 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-开始评估SOP，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:13.096 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - 新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977
2025-07-25 14:42:13.096 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-检查客户SOP条件，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 标签条件: etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, 包含关系: 2
2025-07-25 14:42:13.096 [sop-reevaluation-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4636c0b1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户SOP条件检查结果，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 符合条件: false, 查询结果数量: 0
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户未添加到SOP执行计划，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-SOP客户重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 检查SOP数: 1, 新增执行计划: 0
2025-07-25 14:42:13.126 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:42:18.194 [http-nio-6091-exec-8] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:42:18.217 [http-nio-6091-exec-8] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 原始标签数: 0, 原始标签: []
2025-07-25 14:42:18.396 [http-nio-6091-exec-8] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 原始标签数: 0, 待新增: 2, 待移除: 0, 有实际变更: true
2025-07-25 14:42:18.417 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:42:18.468 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@457d6fae[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.485 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-开始评估SOP，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:18.507 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977
2025-07-25 14:42:18.507 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-检查客户SOP条件，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 标签条件: etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, 包含关系: 2
2025-07-25 14:42:18.508 [sop-reevaluation-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5581799c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.535 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@457d6fae[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.555 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户SOP条件检查结果，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, 符合条件: true, 查询结果数量: 1
2025-07-25 14:42:18.595 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@457d6fae[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:18.692 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-25 14:42:18.717 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:42:18, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:42:18.717 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:42:18, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:42:18.721 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户 wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g 已添加到SOP 1948634773030526977 (0725-testA) 的执行计划中
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户成功添加到SOP执行计划，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977, SOP名称: 0725-testA
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-SOP客户重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 检查SOP数: 1, 新增执行计划: 1
2025-07-25 14:42:18.821 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:42:51.927 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21323316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:51.994 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21323316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:42:52.059 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21323316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:43:57.201 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:44:00.294 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:44:02.165 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:44:03.738 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7d2224a6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:44:03.748 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@16dee87e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:44:25.069 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6886dc3a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:44:30.790 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948635453413744641 创建成功
2025-07-25 14:44:30.937 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:00.517 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:02.112 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:03.623 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:05.082 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:09.386 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@71f42b8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:09.388 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7a203fb0[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:13.398 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:16.541 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6d99a165[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:16.905 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6d99a165[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:17.245 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6d99a165[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:25.471 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@c2e0663[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:25.541 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@c2e0663[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:25.605 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@c2e0663[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:29.729 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:45:29.752 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 原始标签数: 3, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ, etwS5SKgAA0Mq46bDWAw1GopBKIr_PjQ]
2025-07-25 14:45:29.977 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 原始标签数: 3, 待新增: 0, 待移除: 3, 有实际变更: true
2025-07-25 14:45:29.997 [sop-reevaluation-3] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-3
2025-07-25 14:45:30.030 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3aafaf27[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:30.096 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3aafaf27[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:30.156 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1944946052154281986
2025-07-25 14:45:30.159 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3aafaf27[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:30.238 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1943544304063803394
2025-07-25 14:45:30.321 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1943532777965416450
2025-07-25 14:45:30.403 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1943506653168398337
2025-07-25 14:45:30.488 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1945496637924466689
2025-07-25 14:45:30.573 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948018399404908545
2025-07-25 14:45:30.573 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 移除数量: 6
2025-07-25 14:45:30.595 [sop-reevaluation-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo
2025-07-25 14:45:30.595 [sop-reevaluation-3] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-3
2025-07-25 14:45:34.155 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:45:34.179 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 原始标签数: 3, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ, etwS5SKgAAj5t7mr_uy2GRlHw24LHg7A]
2025-07-25 14:45:34.407 [http-nio-6091-exec-6] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 原始标签数: 3, 待新增: 0, 待移除: 3, 有实际变更: true
2025-07-25 14:45:34.427 [sop-reevaluation-4] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-4
2025-07-25 14:45:34.460 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ede267c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:34.526 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ede267c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:34.582 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1943506653168398337
2025-07-25 14:45:34.585 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ede267c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:34.675 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1943532777965416450
2025-07-25 14:45:34.762 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1945496637924466689
2025-07-25 14:45:34.848 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1944946052154281986
2025-07-25 14:45:34.935 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1943544304063803394
2025-07-25 14:45:35.021 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1945496637924466689
2025-07-25 14:45:35.105 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, SOP ID: 1948018399404908545
2025-07-25 14:45:35.105 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 移除数量: 7
2025-07-25 14:45:35.129 [sop-reevaluation-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo
2025-07-25 14:45:35.129 [sop-reevaluation-4] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-4
2025-07-25 14:45:38.980 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:45:39.001 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 原始标签数: 2, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw, etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ]
2025-07-25 14:45:39.221 [http-nio-6091-exec-7] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 原始标签数: 2, 待新增: 0, 待移除: 2, 有实际变更: true
2025-07-25 14:45:39.241 [sop-reevaluation-5] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-5
2025-07-25 14:45:39.273 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@205ae7f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:39.338 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@205ae7f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:39.405 [sop-reevaluation-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, SOP ID: 1948634773030526977
2025-07-25 14:45:39.405 [sop-reevaluation-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户标签变更移除SOP执行计划完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 移除数量: 1
2025-07-25 14:45:39.406 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@205ae7f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:39.426 [sop-reevaluation-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo
2025-07-25 14:45:39.426 [sop-reevaluation-5] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAK5uYDPXK3j-_n8EsQqHk8g, 员工ID: WangWenBo, 线程: sop-reevaluation-5
2025-07-25 14:45:43.166 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:45:44.705 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@151d45a8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:45:44.705 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@750b1a5a[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:16.350 [http-nio-6091-exec-10] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948635896210612225 创建成功
2025-07-25 14:46:16.645 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:46:30.463 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@692956ef[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:30.800 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@692956ef[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:31.130 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@692956ef[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:39.472 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39911acc[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:39.538 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39911acc[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:39.626 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@39911acc[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.609 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:46:44.635 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 原始标签数: 0, 原始标签: []
2025-07-25 14:46:44.794 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 原始标签数: 0, 待新增: 1, 待移除: 0, 有实际变更: true
2025-07-25 14:46:44.815 [sop-reevaluation-6] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-6
2025-07-25 14:46:44.848 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6f7d6881[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.898 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-开始评估SOP，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, SOP名称: A
2025-07-25 14:46:44.912 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6f7d6881[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.919 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - 新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225
2025-07-25 14:46:44.919 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-检查客户SOP条件，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, 标签条件: etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw,etwS5SKgAADKZf7lwlCk95ssmWLGj_sQ, 包含关系: 2
2025-07-25 14:46:44.919 [sop-reevaluation-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4e8e6f54[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:44.967 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户SOP条件检查结果，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, 符合条件: true, 查询结果数量: 1
2025-07-25 14:46:44.979 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6f7d6881[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:45.090 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-weSopExecuteTargets
2025-07-25 14:46:45.115 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:46:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:46:45.115 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - 第1天推送时间已过，延到下一天，基准时间: 2025-07-25 14:46:44, 原推送时间窗口: 00:00:00-00:15:00, 延后推送日期: 2025-07-26
2025-07-25 14:46:45.115 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-targetAttachments
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户 wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg 已添加到SOP 1948635896210612225 (A) 的执行计划中
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-客户成功添加到SOP执行计划，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, SOP ID: 1948635896210612225, SOP名称: A
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-SOP客户重新评估完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 检查SOP数: 1, 新增执行计划: 1
2025-07-25 14:46:45.218 [sop-reevaluation-6] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAA8cMUIrmzoZMpwq5N16lxAg, 员工ID: WangWenBo, 线程: sop-reevaluation-6
2025-07-25 14:46:48.065 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:46:53.502 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:46:55.984 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4539aae7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:46:55.984 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7054a929[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:48:00.880 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:08.668 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:11.369 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:16.217 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1b945bf0[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:48:16.219 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6fe04943[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:48:48.525 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:48:56.831 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:49:15.003 [Thread-94] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 14:49:15.003 [Thread-94] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 14:49:15.004 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 14:49:15.004 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 14:50:31.026 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 14:50:31.076 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:50:31.077 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 14:50:31.077 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 14:50:31.078 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:50:31.079 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 14:50:31.080 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 14:50:31.080 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 14:50:34.982 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:50:34.987 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:50:35.257 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 245 ms. Found 0 Redis repository interfaces.
2025-07-25 14:50:36.033 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 14:50:36.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$d80c0547] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.797 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.802 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.812 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.828 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.830 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.846 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.866 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:36.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:37.657 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 14:50:38.252 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 14:50:38.267 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 14:50:38.268 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 14:50:38.268 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 14:50:38.587 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 14:50:38.587 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7506 ms
2025-07-25 14:50:39.312 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:40.187 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 14:50:41.704 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 14:50:54.570 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:54.585 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:55.194 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:56.477 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:57.415 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 14:50:57.894 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:50:57.906 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 14:50:58.591 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:58.610 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:50:59.434 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:00.667 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:01.161 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:01.442 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:03.377 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:03.609 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:04.609 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:04.673 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:06.003 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:06.747 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 14:51:09.248 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 14:51:09.250 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 14:51:11.102 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 14:51:11.685 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:51:11.686 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 14:51:11.687 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:51:11.961 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 14:51:11.982 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 14:51:11.988 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 14:51:11.990 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 14:51:12.027 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 14:51:12.264 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 46.106 seconds (JVM running for 50.291)
2025-07-25 14:51:12.271 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.273 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:12.274 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.275 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:12.275 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.275 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:12.276 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:51:12.276 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:51:13.047 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:51:13.055 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:51:59.003 [http-nio-6091-exec-4] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 14:51:59.003 [http-nio-6091-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 14:51:59.009 [http-nio-6091-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-25 14:51:59.674 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:52:02.917 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:52:04.725 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@60e7c658[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:04.727 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@52e300df[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:08.348 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4cfb2fa9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:08.735 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4cfb2fa9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:09.068 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4cfb2fa9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:19.466 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@32e31e17[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:19.542 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@32e31e17[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:19.601 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@32e31e17[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:26.035 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:52:26.062 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 原始标签数: 0, 原始标签: []
2025-07-25 14:52:26.230 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 原始标签数: 0, 待新增: 1, 待移除: 0, 有实际变更: true
2025-07-25 14:52:26.254 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:52:26.300 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70f5fb8d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:26.345 [sop-reevaluation-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo
2025-07-25 14:52:26.346 [sop-reevaluation-1] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-1
2025-07-25 14:52:26.369 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70f5fb8d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:26.430 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70f5fb8d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:31.330 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-正式进入makeLable方法
2025-07-25 14:52:31.351 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-获取客户原始标签，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 原始标签数: 1, 原始标签: [etwS5SKgAAYP-6AUtSZnhToVJdoUZMaw]
2025-07-25 14:52:31.518 [http-nio-6091-exec-9] INFO  o.s.s.impl.WeCustomerServiceImpl - makeLabel-客户标签变更检查，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 原始标签数: 1, 待新增: 2, 待移除: 0, 有实际变更: true
2025-07-25 14:52:31.537 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-开始异步处理SOP重新评估，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:52:31.581 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5177d6b3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:31.604 [sop-reevaluation-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - makeLabel-没有找到需要重新评估的SOP，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo
2025-07-25 14:52:31.604 [sop-reevaluation-2] INFO  o.s.l.SopReevaluationEventListener - SOP事件监听器-SOP重新评估完成，客户ID: wmwS5SKgAAtRIDk20n3TT0VEl7xwB8qQ, 员工ID: WangWenBo, 线程: sop-reevaluation-2
2025-07-25 14:52:31.659 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5177d6b3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:31.724 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5177d6b3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:34.394 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:52:36.439 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@16798953[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:52:36.439 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@46fa064[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:53:08.973 [http-nio-6091-exec-4] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [127.0.0.1:5672]
2025-07-25 14:53:09.034 [http-nio-6091-exec-4] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#52b4da7c:0/SimpleConnection@5afb8c3f [delegate=amqp://admin@127.0.0.1:5672/iyqueVhost, localPort= 61655]
2025-07-25 14:53:09.119 [http-nio-6091-exec-4] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948637626549792770 创建成功
2025-07-25 14:53:09.303 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:53:17.535 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:54:24.457 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:54:28.081 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@18c3224e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:54:28.081 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4f1ef0a6[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:54:35.132 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@35a98f23[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:54:37.618 [http-nio-6091-exec-2] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948637998660055042 创建成功
2025-07-25 14:54:37.865 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:55:56.942 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:55:58.611 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:02.412 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:04.165 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:06.342 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@47d8943d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:06.343 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@56b20343[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:30.645 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@21f75419[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:35.861 [http-nio-6091-exec-10] INFO  o.s.s.impl.WeSopBaseServiceImpl - SOP 1948638494602948609 创建成功
2025-07-25 14:56:36.344 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:47.307 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:56:48.806 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7d653b2[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:48.806 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@384ef96e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:54.841 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5ba27316[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:56:57.746 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:57:05.294 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:57:07.251 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2db11cf8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:07.251 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3a2d93f4[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:12.531 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@65cb936[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:44.154 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7f2c4b64[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:44.156 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3ffb1d62[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:48.591 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:57:50.619 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@694e64e9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:57:50.619 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7513ec13[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:03.888 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2ef2bb3e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:23.927 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4aaf61e5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:23.927 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@25ceaf2c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:25.699 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:58:28.832 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:58:30.799 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@70684f5e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:30.799 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5c4fc210[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:36.029 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@74ef00e7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 14:58:52.197 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 14:59:49.790 [Thread-88] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 14:59:49.790 [Thread-88] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 14:59:49.791 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 14:59:49.792 [Thread-3] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:15:35.421 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 15:15:35.447 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:15:35.448 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:15:37.396 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:15:37.399 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:15:37.673 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 257 ms. Found 0 Redis repository interfaces.
2025-07-25 15:15:38.301 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 15:15:38.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$6b48b07] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.906 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.919 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.922 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.928 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/110238970] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.956 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:38.969 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:39.024 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:39.039 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:39.511 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:15:39.962 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 15:15:39.972 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 15:15:39.972 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:15:39.973 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 15:15:40.218 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:15:40.218 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4770 ms
2025-07-25 15:15:40.938 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:41.650 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:15:43.039 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:15:52.295 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:52.308 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:52.711 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:53.562 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:54.183 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 15:15:54.580 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:15:54.590 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:15:54.958 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:54.973 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:55.618 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:57.097 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:57.603 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:57.920 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:00.433 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:00.720 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:01.829 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:01.878 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:03.178 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:03.997 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:05.259 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:16:05.260 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:16:07.010 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:16:07.558 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:16:07.559 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:16:07.560 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:16:07.828 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 15:16:07.856 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 15:16:07.863 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:16:07.864 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:16:07.902 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 15:16:08.617 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 35.509 seconds (JVM running for 37.42)
2025-07-25 15:16:08.628 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.631 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.633 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.633 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.633 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.634 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.634 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:16:08.634 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:16:08.894 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:16:08.902 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:17:01.405 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:17:01.405 [Thread-82] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:17:01.405 [Thread-82] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:17:01.406 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:19:01.244 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:19:01.271 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:19:03.274 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:19:03.277 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:19:03.455 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 0 Redis repository interfaces.
2025-07-25 15:19:04.028 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 15:19:04.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$b84f11c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.569 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.573 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.579 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.587 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.590 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.592 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/499052031] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.616 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.680 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:04.693 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:19:05.180 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:19:05.586 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 15:19:05.596 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 15:19:05.596 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:19:05.596 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 15:19:05.862 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:19:05.862 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4591 ms
2025-07-25 15:19:06.486 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:07.070 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:19:08.379 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:19:17.306 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:17.316 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:17.676 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:19.398 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:19.920 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 15:19:20.194 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:19:20.202 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:19:20.459 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:20.472 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:20.977 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:22.059 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:22.468 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:22.679 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:24.250 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:24.460 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:25.216 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:25.258 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:26.284 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:26.863 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:19:27.938 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:19:27.939 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:19:28.761 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:19:28.963 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:19:28.964 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:19:28.965 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:19:29.167 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 15:19:29.180 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 15:19:29.183 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:19:29.184 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:19:29.213 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 15:19:29.354 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 30.936 seconds (JVM running for 33.326)
2025-07-25 15:19:29.360 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.361 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:29.362 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.362 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:29.362 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.362 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:29.363 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:19:29.363 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:19:30.261 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:30.268 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:21:13.360 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:21:13.360 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:21:13.366 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-25 15:21:13.503 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:21:16.453 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6ca9fa1f[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:21:16.460 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3ec5021b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:21:49.776 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:21:59.153 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@59110ac4[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:21:59.161 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7debcb6e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:23:02.723 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:23:47.802 [http-nio-6091-exec-5] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:31:17.523 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:31:17.523 [Thread-74] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:31:17.524 [Thread-74] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:31:17.524 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:33:09.166 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-25 15:33:09.184 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:33:09.185 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:33:10.932 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:33:10.934 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:33:11.103 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 156 ms. Found 0 Redis repository interfaces.
2025-07-25 15:33:11.557 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-25 15:33:11.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$dc4ae769] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.982 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.989 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.992 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:11.999 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.010 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.059 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.073 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:33:12.461 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:33:12.807 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-25 15:33:12.817 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-25 15:33:12.818 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:33:12.818 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-25 15:33:13.093 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:33:13.094 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3909 ms
2025-07-25 15:33:13.626 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:14.162 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:33:15.384 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:33:24.043 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:24.054 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:24.387 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:25.125 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:25.649 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-25 15:33:25.956 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:33:25.964 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-25 15:33:26.253 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:26.266 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:26.849 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:27.859 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:28.329 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:28.576 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:30.224 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:30.403 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:31.259 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:31.318 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:32.358 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:33.075 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:33:34.280 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:33:34.281 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:33:35.108 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:33:35.328 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:33:35.328 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:33:35.329 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:33:35.534 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-25 15:33:35.546 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-25 15:33:35.550 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:33:35.551 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:33:35.582 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-25 15:33:35.727 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 28.444 seconds (JVM running for 29.966)
2025-07-25 15:33:35.733 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.734 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:35.735 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.735 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:35.735 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.735 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:35.736 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:33:35.736 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:33:36.620 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:33:36.626 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 15:35:21.944 [http-nio-6091-exec-2] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:35:21.944 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:35:21.948 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-25 15:35:22.087 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:35:35.002 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:35:36.922 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@704bf070[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:36.922 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2ddd855e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:41.056 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@7e8a17f3[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:47.707 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3056f223[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:50.771 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-25 15:35:53.288 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@2808b7d1[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:35:53.288 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=48756c5c-d0f6-4d4e-98c3-e56881504dee, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753425517178, expireTime=1753468717178, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@31552536[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-25 15:36:06.037 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:36:06.037 [Thread-70] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:36:06.037 [Thread-70] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:36:06.038 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:36:06.203 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 15:36:06.204 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-web-api:**************:6091 from beat map.
2025-07-25 15:36:06.204 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 15:36:06.239 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 15:36:06.239 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-25 15:36:06.856 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-25 15:36:06.857 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
