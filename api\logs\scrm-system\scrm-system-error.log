2025-07-25 15:16:46.576 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getDeclaredAnnotations(Class.java:3477)
	at org.springframework.core.annotation.AnnotationsScanner.getDeclaredAnnotations(AnnotationsScanner.java:454)
	at org.springframework.core.annotation.AnnotationsScanner.isKnownEmpty(AnnotationsScanner.java:492)
	at org.springframework.core.annotation.TypeMappedAnnotations.from(TypeMappedAnnotations.java:251)
	at org.springframework.core.annotation.MergedAnnotations.from(MergedAnnotations.java:351)
	at org.springframework.core.annotation.MergedAnnotations.from(MergedAnnotations.java:330)
	at org.springframework.core.type.StandardAnnotationMetadata.<init>(StandardAnnotationMetadata.java:86)
	at org.springframework.core.type.StandardAnnotationMetadata.from(StandardAnnotationMetadata.java:175)
	at org.springframework.core.type.AnnotationMetadata.introspect(AnnotationMetadata.java:127)
	at org.springframework.beans.factory.annotation.AnnotatedGenericBeanDefinition.<init>(AnnotatedGenericBeanDefinition.java:58)
	at org.springframework.context.annotation.AnnotatedBeanDefinitionReader.doRegisterBean(AnnotatedBeanDefinitionReader.java:253)
	at org.springframework.context.annotation.AnnotatedBeanDefinitionReader.registerBean(AnnotatedBeanDefinitionReader.java:147)
	at org.springframework.context.annotation.AnnotatedBeanDefinitionReader.register(AnnotatedBeanDefinitionReader.java:137)
	at org.springframework.boot.BeanDefinitionLoader.load(BeanDefinitionLoader.java:171)
	at org.springframework.boot.BeanDefinitionLoader.load(BeanDefinitionLoader.java:146)
	at org.springframework.boot.BeanDefinitionLoader.load(BeanDefinitionLoader.java:139)
	at org.springframework.boot.SpringApplication.load(SpringApplication.java:685)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:407)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:302)
	at org.scrm.ScrmSystemApplication.main(ScrmSystemApplication.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
