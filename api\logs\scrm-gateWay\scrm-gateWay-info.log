2025-07-25 14:34:57.101 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-25 14:34:57.139 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:34:57.139 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 14:34:57.145 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 14:34:57.145 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:34:57.145 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 14:34:57.145 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 14:34:57.145 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 14:34:57.910 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:34:57.912 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:34:57.954 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-25 14:34:58.164 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-25 14:34:58.240 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-25 14:34:58.574 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.581 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.583 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.588 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.593 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.596 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.597 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.609 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.652 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.663 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.746 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 14:34:58.750 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.751 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:58.752 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:34:59.992 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-25 14:35:00.453 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-25 14:35:00.500 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-25 14:35:01.632 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-25 14:35:01.738 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:35:01.739 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 14:35:01.739 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:35:01.918 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 14:35:01.925 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-25 14:35:01.934 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-25 14:35:01.942 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-25 14:35:01.947 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 14:35:01.955 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-25 14:35:01.961 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-25 14:35:01.969 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:35:01.970 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:35:02.390 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-25 14:35:02.768 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-25 14:35:02.919 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 14:35:04.158 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 14:35:04.600 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 14:35:04.601 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 14:35:05.487 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 14:35:05.713 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-25 14:35:05.985 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 14:35:05.985 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 14:35:06.021 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-25 14:35:06.103 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 10.781 seconds (JVM running for 13.361)
2025-07-25 14:35:06.108 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:35:06.109 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:35:06.110 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:35:06.110 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:35:06.111 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:35:06.111 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:35:06.112 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:35:06.112 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:35:06.120 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:35:06.126 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:35:06.725 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:35:06.727 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:05.911 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:05.913 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:35.605 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/auth/login 业务请求traceId:iyque-1753425515605-1359
2025-07-25 14:38:35.790 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:35.793 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:38:37.907 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753425517907-1913
2025-07-25 14:38:38.816 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753425518816-6850
2025-07-25 14:38:38.818 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753425518818-2356
2025-07-25 14:38:42.232 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/getWeIndex 业务请求traceId:iyque-1753425522232-6830
2025-07-25 14:38:44.780 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/getRealCustomerCnt 业务请求traceId:iyque-1753425524780-3532
2025-07-25 14:38:44.786 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/operation/group/member/getGroupMemberRealNoPageCnt 业务请求traceId:iyque-1753425524786-811
2025-07-25 14:38:44.918 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/trajectory/findCompanyDynamics 业务请求traceId:iyque-1753425524918-8390
2025-07-25 14:38:45.287 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/trajectory/findCompanyDynamics 业务请求traceId:iyque-1753425525287-3833
2025-07-25 14:39:04.176 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425544176-9106
2025-07-25 14:39:10.133 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753425550133-9540
2025-07-25 14:39:10.237 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425550237-5089
2025-07-25 14:39:11.708 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948018399404908545 业务请求traceId:iyque-1753425551708-403
2025-07-25 14:39:11.946 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425551946-5999
2025-07-25 14:39:14.927 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753425554927-5575
2025-07-25 14:39:14.927 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753425554927-3511
2025-07-25 14:39:25.312 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753425565312-4182
2025-07-25 14:39:25.660 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425565660-5978
2025-07-25 14:39:32.365 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425572365-7572
2025-07-25 14:39:36.644 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425576644-6248
2025-07-25 14:39:45.257 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753425585257-4286
2025-07-25 14:39:51.775 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753425591775-7791
2025-07-25 14:39:52.409 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425592409-9924
2025-07-25 14:39:54.235 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425594235-9732
2025-07-25 14:39:54.235 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948634283865628673 业务请求traceId:iyque-1753425594235-6717
2025-07-25 14:39:54.235 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948634283865628673 业务请求traceId:iyque-1753425594235-6995
2025-07-25 14:39:56.140 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425596140-1458
2025-07-25 14:39:58.876 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425598876-372
2025-07-25 14:39:58.876 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findCustomerAddWay 业务请求traceId:iyque-1753425598876-9338
2025-07-25 14:39:58.876 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425598876-8014
2025-07-25 14:39:58.876 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753425598876-937
2025-07-25 14:40:00.851 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425600851-1203
2025-07-25 14:40:04.783 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425604783-6632
2025-07-25 14:40:08.596 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425608596-3772
2025-07-25 14:40:35.875 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425635875-5359
2025-07-25 14:40:41.138 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948634283865628673 业务请求traceId:iyque-1753425641138-5113
2025-07-25 14:40:41.138 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425641138-457
2025-07-25 14:40:41.138 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948634283865628673 业务请求traceId:iyque-1753425641138-4406
2025-07-25 14:40:47.795 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425647795-716
2025-07-25 14:40:49.975 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753425649974-472
2025-07-25 14:40:50.048 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425650048-8034
2025-07-25 14:40:51.830 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948634283865628673 业务请求traceId:iyque-1753425651830-658
2025-07-25 14:40:52.064 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425652064-2450
2025-07-25 14:40:54.365 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753425654365-4453
2025-07-25 14:40:54.365 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753425654365-85
2025-07-25 14:40:56.196 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753425656196-431
2025-07-25 14:41:03.912 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425663912-1242
2025-07-25 14:41:07.539 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425667539-9203
2025-07-25 14:41:20.810 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425680810-9328
2025-07-25 14:41:48.406 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753425708406-8280
2025-07-25 14:41:48.747 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425708747-7328
2025-07-25 14:41:56.925 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425716925-6711
2025-07-25 14:41:56.925 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425716925-4079
2025-07-25 14:41:58.906 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425718906-6278
2025-07-25 14:42:02.330 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425722330-1311
2025-07-25 14:42:06.180 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425726180-4956
2025-07-25 14:42:09.307 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425729307-7073
2025-07-25 14:42:11.712 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753425731712-4105
2025-07-25 14:42:12.078 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425732078-6272
2025-07-25 14:42:18.168 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753425738168-4776
2025-07-25 14:42:18.461 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425738461-7008
2025-07-25 14:42:51.918 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425771918-4468
2025-07-25 14:43:36.146 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:43:36.150 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 14:43:57.194 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425837194-1886
2025-07-25 14:44:00.221 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753425840221-5559
2025-07-25 14:44:00.289 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425840289-5242
2025-07-25 14:44:01.918 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948634773030526977 业务请求traceId:iyque-1753425841918-4291
2025-07-25 14:44:02.152 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425842152-9119
2025-07-25 14:44:03.729 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753425843729-8444
2025-07-25 14:44:03.730 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753425843730-9585
2025-07-25 14:44:09.247 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753425849247-2624
2025-07-25 14:44:09.783 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425849783-9551
2025-07-25 14:44:15.747 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425855747-4320
2025-07-25 14:44:19.624 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425859624-666
2025-07-25 14:44:25.065 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753425865065-4763
2025-07-25 14:44:30.622 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753425870622-8483
2025-07-25 14:44:30.934 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425870934-9837
2025-07-25 14:44:33.766 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948635453413744641 业务请求traceId:iyque-1753425873766-1145
2025-07-25 14:44:33.766 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948635453413744641 业务请求traceId:iyque-1753425873766-1063
2025-07-25 14:44:33.767 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425873767-862
2025-07-25 14:44:34.842 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425874842-3878
2025-07-25 14:44:35.426 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425875426-2749
2025-07-25 14:44:45.373 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425885373-853
2025-07-25 14:44:45.884 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425885884-5194
2025-07-25 14:44:46.059 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425886059-1299
2025-07-25 14:44:46.267 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425886267-5140
2025-07-25 14:44:47.875 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425887875-392
2025-07-25 14:44:48.813 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425888813-7529
2025-07-25 14:44:58.428 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425898428-3458
2025-07-25 14:44:58.991 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753425898991-3492
2025-07-25 14:45:00.511 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425900511-8389
2025-07-25 14:45:02.109 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425902109-9040
2025-07-25 14:45:03.564 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753425903564-57
2025-07-25 14:45:03.618 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425903618-1898
2025-07-25 14:45:04.877 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948635453413744641 业务请求traceId:iyque-1753425904877-7803
2025-07-25 14:45:05.079 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425905079-6775
2025-07-25 14:45:09.377 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753425909377-3241
2025-07-25 14:45:09.377 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753425909377-2369
2025-07-25 14:45:10.942 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753425910942-9583
2025-07-25 14:45:13.394 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425913394-9983
2025-07-25 14:45:16.535 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425916535-2133
2025-07-25 14:45:16.535 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425916535-9300
2025-07-25 14:45:18.861 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425918861-964
2025-07-25 14:45:22.569 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425922569-1615
2025-07-25 14:45:25.467 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425925467-9971
2025-07-25 14:45:27.051 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425927051-5817
2025-07-25 14:45:29.704 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753425929704-4321
2025-07-25 14:45:30.026 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425930026-315
2025-07-25 14:45:33.208 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425933208-3695
2025-07-25 14:45:34.130 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753425934130-6550
2025-07-25 14:45:34.455 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425934455-9104
2025-07-25 14:45:38.955 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753425938955-8146
2025-07-25 14:45:39.268 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425939268-5877
2025-07-25 14:45:43.163 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425943163-5624
2025-07-25 14:45:44.699 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753425944699-5617
2025-07-25 14:45:44.699 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753425944699-5973
2025-07-25 14:45:47.278 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753425947278-351
2025-07-25 14:45:47.885 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425947885-2920
2025-07-25 14:45:55.059 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425955059-7762
2025-07-25 14:46:01.015 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425961015-7290
2025-07-25 14:46:16.198 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753425976198-5275
2025-07-25 14:46:16.642 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753425976642-553
2025-07-25 14:46:30.445 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425990445-4658
2025-07-25 14:46:30.445 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753425990445-6327
2025-07-25 14:46:32.480 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753425992480-3732
2025-07-25 14:46:36.388 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753425996388-3531
2025-07-25 14:46:39.467 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753425999467-8549
2025-07-25 14:46:42.226 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753426002226-835
2025-07-25 14:46:44.582 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753426004582-9355
2025-07-25 14:46:44.842 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753426004842-8972
2025-07-25 14:46:48.062 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426008062-688
2025-07-25 14:46:49.686 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948635896210612225 业务请求traceId:iyque-1753426009686-8008
2025-07-25 14:46:49.686 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948635896210612225 业务请求traceId:iyque-1753426009686-2428
2025-07-25 14:46:49.686 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426009686-9220
2025-07-25 14:46:50.617 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753426010617-4072
2025-07-25 14:46:53.499 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426013499-5303
2025-07-25 14:46:55.978 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948635896210612225 业务请求traceId:iyque-1753426015978-1803
2025-07-25 14:46:55.979 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426015979-6009
2025-07-25 14:46:55.979 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426015979-3717
2025-07-25 14:46:56.172 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426016172-3002
2025-07-25 14:47:19.914 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753426039914-5987
2025-07-25 14:48:00.876 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426080876-7586
2025-07-25 14:48:08.613 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753426088613-3496
2025-07-25 14:48:08.666 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426088666-6260
2025-07-25 14:48:11.359 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426091359-5599
2025-07-25 14:48:16.206 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948635896210612225 业务请求traceId:iyque-1753426096206-914
2025-07-25 14:48:16.207 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426096207-4492
2025-07-25 14:48:16.210 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426096210-5139
2025-07-25 14:48:16.492 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426096491-2688
2025-07-25 14:48:37.728 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753426117728-2246
2025-07-25 14:48:48.521 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426128521-2438
2025-07-25 14:48:52.128 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426132128-1028
2025-07-25 14:48:52.128 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948635896210612225 业务请求traceId:iyque-1753426132128-2185
2025-07-25 14:48:52.128 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948635896210612225 业务请求traceId:iyque-1753426132128-6518
2025-07-25 14:48:53.816 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753426133816-9772
2025-07-25 14:48:56.829 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426136829-8860
2025-07-25 14:49:14.827 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 14:49:14.827 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 14:49:14.827 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 14:49:14.828 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 14:49:14.836 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-25 14:49:14.869 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-25 14:49:14.869 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 14:49:14.870 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-25 14:49:14.870 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 14:49:14.902 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 14:49:14.902 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-25 14:49:48.107 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-25 14:49:48.131 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:49:48.131 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 14:49:48.131 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 14:49:48.131 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 14:49:48.131 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 14:49:48.132 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 14:49:48.132 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 14:49:48.918 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:49:48.920 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:49:48.962 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-25 14:49:49.144 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-25 14:49:49.214 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-25 14:49:49.491 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.499 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.502 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.507 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.514 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.517 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.519 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/36266279] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.524 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.535 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.580 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.592 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.672 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 14:49:49.675 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.677 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:49.678 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:49:50.759 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-25 14:49:51.257 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-25 14:49:51.305 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 14:49:52.445 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-25 14:49:52.445 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-25 14:49:52.446 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-25 14:49:52.555 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:49:52.555 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 14:49:52.555 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 14:49:52.812 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 14:49:52.819 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-25 14:49:52.827 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-25 14:49:52.833 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-25 14:49:52.840 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 14:49:52.846 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-25 14:49:52.853 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-25 14:49:52.867 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:49:52.868 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:49:53.421 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-25 14:49:53.634 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-25 14:49:53.784 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 14:49:54.907 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 14:49:55.337 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 14:49:55.338 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 14:49:56.113 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 14:49:56.268 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-25 14:49:56.550 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 14:49:56.550 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 14:49:56.587 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-25 14:49:56.643 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 11.356 seconds (JVM running for 15.037)
2025-07-25 14:49:56.647 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:49:56.648 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:49:56.648 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:49:56.649 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:49:56.650 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:49:56.650 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:49:56.651 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 14:49:56.651 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-25 14:49:57.616 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:49:57.625 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:49:59.946 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:49:59.947 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:55.095 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948635896210612225 业务请求traceId:iyque-1753426315095-6008
2025-07-25 14:51:55.320 [boundedElastic-9] WARN  o.s.c.l.core.RoundRobinLoadBalancer - No servers available for service: scrm-web-api
2025-07-25 14:51:55.366 [boundedElastic-9] ERROR o.s.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/open/sop/1948635896210612225,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for scrm-web-api"
2025-07-25 14:51:56.143 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:56.144 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:56.177 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:56.178 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:56.182 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:56.183 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:51:58.806 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948635896210612225 业务请求traceId:iyque-1753426318806-8350
2025-07-25 14:51:59.523 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426319523-7658
2025-07-25 14:52:01.599 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753426321598-5219
2025-07-25 14:52:02.431 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753426322431-3435
2025-07-25 14:52:02.431 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753426322431-7603
2025-07-25 14:52:02.911 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426322911-5827
2025-07-25 14:52:04.614 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426324613-8837
2025-07-25 14:52:04.614 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426324614-5378
2025-07-25 14:52:08.312 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426328312-4700
2025-07-25 14:52:08.312 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findCustomerAddWay 业务请求traceId:iyque-1753426328312-4537
2025-07-25 14:52:08.315 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753426328315-6818
2025-07-25 14:52:08.315 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753426328315-2185
2025-07-25 14:52:11.420 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753426331420-5277
2025-07-25 14:52:16.140 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753426336140-5315
2025-07-25 14:52:19.460 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753426339460-3885
2025-07-25 14:52:23.845 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753426343845-410
2025-07-25 14:52:25.986 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753426345986-3980
2025-07-25 14:52:26.289 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753426346289-8146
2025-07-25 14:52:31.305 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/makeLabel 业务请求traceId:iyque-1753426351305-1961
2025-07-25 14:52:31.569 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findWeCustomerList 业务请求traceId:iyque-1753426351569-5496
2025-07-25 14:52:34.390 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426354390-1758
2025-07-25 14:52:36.431 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426356431-3214
2025-07-25 14:52:36.432 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426356432-8557
2025-07-25 14:52:49.380 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426369380-6841
2025-07-25 14:52:49.878 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753426369878-7680
2025-07-25 14:52:52.629 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753426372629-9194
2025-07-25 14:52:59.281 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753426379281-124
2025-07-25 14:53:08.711 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753426388711-2910
2025-07-25 14:53:09.300 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426389300-5980
2025-07-25 14:53:17.530 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426397530-4564
2025-07-25 14:53:18.557 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948637626549792770 业务请求traceId:iyque-1753426398557-4109
2025-07-25 14:53:18.557 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948637626549792770 业务请求traceId:iyque-1753426398557-749
2025-07-25 14:53:18.557 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426398557-7184
2025-07-25 14:54:22.955 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426462955-8217
2025-07-25 14:54:23.403 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426463402-6169
2025-07-25 14:54:23.562 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426463562-2527
2025-07-25 14:54:24.454 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426464454-4110
2025-07-25 14:54:28.070 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948637626549792770 业务请求traceId:iyque-1753426468070-7649
2025-07-25 14:54:28.072 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426468072-744
2025-07-25 14:54:28.072 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426468072-4180
2025-07-25 14:54:28.283 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426468283-4439
2025-07-25 14:54:35.126 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753426475126-9169
2025-07-25 14:54:37.462 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753426477462-8179
2025-07-25 14:54:37.858 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426477858-1118
2025-07-25 14:55:27.067 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - modified ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:55:27.068 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:55:47.160 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:55:47.161 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-task -> []
2025-07-25 14:55:53.235 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:55:53.237 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:55:56.863 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753426556863-9590
2025-07-25 14:55:56.937 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426556937-5839
2025-07-25 14:55:58.424 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948637626549792770 业务请求traceId:iyque-1753426558424-9226
2025-07-25 14:55:58.607 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426558607-8285
2025-07-25 14:56:02.357 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753426562357-9905
2025-07-25 14:56:02.408 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426562408-9381
2025-07-25 14:56:03.975 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/1948637998660055042 业务请求traceId:iyque-1753426563974-1305
2025-07-25 14:56:04.161 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426564161-9651
2025-07-25 14:56:06.336 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426566336-5552
2025-07-25 14:56:06.336 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426566336-53
2025-07-25 14:56:18.754 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426578754-2716
2025-07-25 14:56:19.295 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/dept/list 业务请求traceId:iyque-1753426579295-4205
2025-07-25 14:56:22.058 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/findUserList 业务请求traceId:iyque-1753426582058-7150
2025-07-25 14:56:26.289 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/tag/list 业务请求traceId:iyque-1753426586289-4323
2025-07-25 14:56:30.640 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753426590640-8466
2025-07-25 14:56:35.709 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/createWeSop 业务请求traceId:iyque-1753426595709-8804
2025-07-25 14:56:36.324 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426596324-3076
2025-07-25 14:56:43.154 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426603154-5068
2025-07-25 14:56:43.154 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426603154-1854
2025-07-25 14:56:43.155 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948638494602948609 业务请求traceId:iyque-1753426603155-9582
2025-07-25 14:56:44.497 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753426604497-7870
2025-07-25 14:56:47.304 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426607304-3309
2025-07-25 14:56:48.800 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426608800-473
2025-07-25 14:56:48.801 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426608801-6586
2025-07-25 14:56:48.801 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426608801-8422
2025-07-25 14:56:48.944 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426608944-5896
2025-07-25 14:56:54.836 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753426614836-5613
2025-07-25 14:56:57.743 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426617743-1065
2025-07-25 14:56:59.299 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948638494602948609 业务请求traceId:iyque-1753426619299-9342
2025-07-25 14:56:59.299 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753426619299-1937
2025-07-25 14:56:59.299 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426619299-5700
2025-07-25 14:57:00.106 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findCustomerExecuteContent 业务请求traceId:iyque-1753426620106-777
2025-07-25 14:57:05.290 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426625290-2138
2025-07-25 14:57:07.245 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426627245-2042
2025-07-25 14:57:07.245 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426627245-7801
2025-07-25 14:57:07.245 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426627245-7425
2025-07-25 14:57:07.402 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426627402-258
2025-07-25 14:57:12.524 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753426632524-9629
2025-07-25 14:57:23.870 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753426643870-6991
2025-07-25 14:57:43.226 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753426663226-6012
2025-07-25 14:57:43.462 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753426663462-5881
2025-07-25 14:57:43.462 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753426663462-8296
2025-07-25 14:57:44.140 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426664140-9793
2025-07-25 14:57:44.140 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426664140-8084
2025-07-25 14:57:44.141 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426664141-627
2025-07-25 14:57:44.577 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426664577-9018
2025-07-25 14:57:48.582 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426668582-514
2025-07-25 14:57:50.613 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426670613-2360
2025-07-25 14:57:50.613 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426670613-5778
2025-07-25 14:57:50.613 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426670613-7444
2025-07-25 14:57:50.883 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426670883-3626
2025-07-25 14:58:03.880 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753426683880-2535
2025-07-25 14:58:13.937 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753426693937-3667
2025-07-25 14:58:23.202 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753426703202-7711
2025-07-25 14:58:23.367 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753426703367-1118
2025-07-25 14:58:23.367 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753426703367-1421
2025-07-25 14:58:23.920 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426703920-4808
2025-07-25 14:58:23.921 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426703921-7489
2025-07-25 14:58:23.921 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426703921-3044
2025-07-25 14:58:24.216 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426704216-4215
2025-07-25 14:58:25.696 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426705696-1669
2025-07-25 14:58:28.774 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateSopState 业务请求traceId:iyque-1753426708774-79
2025-07-25 14:58:28.829 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426708829-7419
2025-07-25 14:58:30.792 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753426710792-5815
2025-07-25 14:58:30.794 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753426710794-9860
2025-07-25 14:58:30.794 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753426710794-4708
2025-07-25 14:58:31.006 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753426711006-8920
2025-07-25 14:58:36.022 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753426716022-6630
2025-07-25 14:58:48.598 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/updateWeSop 业务请求traceId:iyque-1753426728598-8120
2025-07-25 14:58:52.190 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753426732190-3065
2025-07-25 14:59:34.417 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - modified ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:59:34.423 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:59:44.480 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-25 14:59:44.485 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-task -> []
2025-07-25 14:59:49.518 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 14:59:49.519 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 14:59:49.519 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 14:59:49.520 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 14:59:49.526 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-25 14:59:49.537 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-25 14:59:49.537 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 14:59:49.538 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-25 14:59:49.538 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 14:59:49.574 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 14:59:49.574 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-25 15:14:53.804 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:14:53.818 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:14:54.568 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:14:54.570 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:14:54.611 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-25 15:14:54.792 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-25 15:14:54.867 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-25 15:14:55.137 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.146 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.148 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.159 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.162 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.163 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.168 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.176 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.219 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.230 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.313 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:14:55.316 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:55.319 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:14:56.408 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-25 15:14:56.861 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-25 15:14:56.887 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-25 15:14:58.086 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-25 15:14:58.087 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-25 15:14:58.087 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-25 15:14:58.087 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-25 15:14:58.087 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-25 15:14:58.087 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-25 15:14:58.220 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:14:58.220 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:14:58.221 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:14:58.394 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:14:58.402 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-25 15:14:58.412 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-25 15:14:58.419 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-25 15:14:58.424 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:14:58.430 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-25 15:14:58.438 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-25 15:14:58.448 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:14:58.449 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:14:58.981 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-25 15:14:59.202 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-25 15:14:59.400 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:15:00.614 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:15:01.075 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:15:01.077 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:15:01.910 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:15:02.116 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-25 15:15:02.376 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:15:02.377 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:15:02.413 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-25 15:15:02.465 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 11.437 seconds (JVM running for 13.112)
2025-07-25 15:15:02.468 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:15:02.468 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:15:02.468 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:15:02.468 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:15:02.469 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:15:02.469 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:15:02.470 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:15:02.470 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:15:02.513 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:15:02.522 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:15:03.214 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:15:03.216 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:32.208 [boundedElastic-2] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:32.210 [boundedElastic-2] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:32.234 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:32.236 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:32.237 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:32.238 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:43.311 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:16:43.312 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-system -> []
2025-07-25 15:17:01.254 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:17:01.254 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:17:01.254 [Thread-31] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:17:01.255 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:17:01.261 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-25 15:17:01.269 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-25 15:17:01.269 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 15:17:01.270 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-25 15:17:01.270 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 15:17:01.306 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 15:17:01.306 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-25 15:17:26.760 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:17:26.774 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:17:27.509 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:17:27.512 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:17:27.554 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-25 15:17:27.757 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-25 15:17:27.836 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-25 15:17:28.167 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.185 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.192 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.200 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.203 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.204 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.211 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.223 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.281 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.297 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.389 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:17:28.394 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:28.397 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:17:29.562 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-25 15:17:30.006 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-25 15:17:30.020 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-25 15:17:31.138 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-25 15:17:31.255 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:17:31.256 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:17:31.256 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:17:31.506 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:31.506 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:31.511 [boundedElastic-3] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:31.511 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:31.513 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:17:31.520 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-25 15:17:31.528 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-25 15:17:31.534 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-25 15:17:31.540 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:17:31.546 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-25 15:17:31.552 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-25 15:17:31.580 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:17:31.581 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:17:32.162 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-25 15:17:32.379 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-25 15:17:32.566 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:17:33.616 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:17:34.072 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:17:34.073 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:17:34.876 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:17:35.035 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-25 15:17:35.299 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:17:35.300 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:17:35.339 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-25 15:17:35.394 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 10.389 seconds (JVM running for 11.678)
2025-07-25 15:17:35.397 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:17:35.397 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:17:35.398 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:17:35.398 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:17:35.399 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:17:35.399 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:17:35.399 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:17:35.399 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:17:36.098 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:36.100 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:38.606 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:38.608 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:42.583 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:42.585 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-task -> []
2025-07-25 15:17:42.587 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:17:42.588 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-web-api -> []
2025-07-25 15:19:34.826 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:34.827 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:44.835 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:44.836 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:44.897 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:19:44.899 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-task -> [{"instanceId":"**************#6400#DEFAULT#DEFAULT_GROUP@@scrm-task","ip":"**************","port":6400,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-task","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:21:11.312 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753428071312-5504
2025-07-25 15:21:12.500 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753428072500-9443
2025-07-25 15:21:12.500 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753428072500-3831
2025-07-25 15:21:13.171 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428073171-6906
2025-07-25 15:21:16.375 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753428076375-9792
2025-07-25 15:21:16.376 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753428076375-1522
2025-07-25 15:21:16.378 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753428076377-2231
2025-07-25 15:21:16.782 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753428076782-2651
2025-07-25 15:21:49.768 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428109768-5979
2025-07-25 15:21:59.129 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753428119129-2142
2025-07-25 15:21:59.129 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753428119129-3389
2025-07-25 15:21:59.129 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753428119129-8023
2025-07-25 15:21:59.435 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753428119435-6001
2025-07-25 15:23:02.715 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428182715-6914
2025-07-25 15:23:47.291 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753428227291-3952
2025-07-25 15:23:47.456 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753428227456-8497
2025-07-25 15:23:47.456 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753428227456-9508
2025-07-25 15:23:47.798 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428227798-4965
2025-07-25 15:31:17.366 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:31:17.366 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:31:17.367 [Thread-29] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:31:17.367 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:31:17.377 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-25 15:31:17.388 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-25 15:31:17.389 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 15:31:17.390 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-25 15:31:17.390 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 15:31:17.425 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 15:31:17.425 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-25 15:32:48.379 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-25 15:32:48.397 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-25 15:32:49.293 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 15:32:49.295 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:32:49.338 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-25 15:32:49.519 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-25 15:32:49.589 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-25 15:32:49.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.879 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.881 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.886 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.891 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.893 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.894 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/774610771] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.900 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.908 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:49.962 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:50.047 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-25 15:32:50.051 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:50.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:50.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:32:51.163 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-25 15:32:51.701 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-25 15:32:51.727 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:32:52.903 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-25 15:32:52.904 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-25 15:32:53.041 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:32:53.041 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-25 15:32:53.041 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-25 15:32:53.297 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:32:53.303 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-25 15:32:53.310 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-25 15:32:53.315 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-25 15:32:53.320 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-25 15:32:53.326 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-25 15:32:53.332 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-25 15:32:53.340 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:32:53.340 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:32:53.874 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-25 15:32:54.093 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-25 15:32:54.245 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-25 15:32:55.420 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 15:32:55.899 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-25 15:32:55.901 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-25 15:32:56.678 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-25 15:32:56.838 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-25 15:32:57.098 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-25 15:32:57.098 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 15:32:57.133 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-25 15:32:57.181 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 11.595 seconds (JVM running for 13.521)
2025-07-25 15:32:57.183 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:32:57.183 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:32:57.184 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:32:57.184 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:32:57.185 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:32:57.185 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:32:57.186 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-25 15:32:57.186 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-25 15:32:57.222 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:32:57.225 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:32:57.868 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:32:57.870 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:33:56.583 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:33:56.584 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:33:56.929 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:33:56.934 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 15:35:20.373 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753428920373-680
2025-07-25 15:35:21.366 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753428921366-4684
2025-07-25 15:35:21.366 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753428921366-534
2025-07-25 15:35:21.806 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428921806-4340
2025-07-25 15:35:34.568 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753428934568-6991
2025-07-25 15:35:34.719 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753428934719-595
2025-07-25 15:35:34.719 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753428934719-7186
2025-07-25 15:35:34.996 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428934996-6432
2025-07-25 15:35:36.848 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753428936848-4719
2025-07-25 15:35:36.848 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753428936848-9202
2025-07-25 15:35:36.848 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753428936848-9477
2025-07-25 15:35:37.223 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753428937223-4499
2025-07-25 15:35:41.035 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753428941035-3440
2025-07-25 15:35:47.699 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753428947699-8408
2025-07-25 15:35:50.767 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753428950767-8599
2025-07-25 15:35:53.280 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753428953280-4404
2025-07-25 15:35:53.280 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753428953280-5395
2025-07-25 15:35:53.280 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948638494602948609 业务请求traceId:iyque-1753428953280-8157
2025-07-25 15:35:53.545 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753428953545-4447
2025-07-25 15:36:06.169 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 15:36:06.172 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-25 15:36:06.172 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-25 15:36:06.173 [Thread-30] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-25 15:36:06.181 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-25 15:36:06.191 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-25 15:36:06.191 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 15:36:06.192 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@scrm-gateWay:**************:6180 from beat map.
2025-07-25 15:36:06.192 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 deregistering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-07-25 15:36:06.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 15:36:06.230 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
