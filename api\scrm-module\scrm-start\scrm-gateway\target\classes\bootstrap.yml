spring:
  profiles:
    active: '@spring.profiles.active@'
---
spring:
  profiles: dev
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      config:
        namespace: 16f3da13-e0bb-4941-84c2-b352c7b39505
        server-addr: 121.40.36.84:8848
        file-extension: yml
        config-retry-time: 300000
        shared-configs[0]:
          dataId: scrm-common.yml
          refresh: true
        username: nacos
        password: nacos
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
---
spring:
  profiles: test
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      config:
        namespace: ewscrm
        server-addr: 121.40.36.84:8848
        file-extension: yml
        config-retry-time: 300000
        shared-configs[0]:
          dataId: scrm-common.yml
          refresh: true
        username:
        password:
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
---
spring:
  profiles: prod
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      config:
        namespace: 8adbbf24-ada9-4f9d-9032-1a279e23d3ac
        server-addr: 127.0.0.1:8848
        file-extension: yml
        config-retry-time: 300000
        shared-configs[0]:
          dataId: scrm-common.yml
          refresh: true
        username: nacos
        password: nacos
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}