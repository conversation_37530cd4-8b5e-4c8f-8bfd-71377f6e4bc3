2025-07-25 14:38:46.499 [http-nio-6091-exec-2] ERROR c.a.druid.filter.stat.StatFilter - slow sql 1633 millis. SELECT
            date as xTime,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
             AND del_flag=0
              
            ) as totalCnt,

          ((SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
            AND del_flag=0
             
            ) - (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')<=date
            AND del_flag=0
             
            )) as repeatCnt,

            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date
                 AND del_flag=0
                 
            ) as addCnt,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and track_state=5
                 
             ) as lostCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and del_flag=1
             
            ) as userDelCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date and del_flag=0 and track_state!=5
             
           ) as netCnt,
            (SELECT IFNULL(sum(new_apply_cnt),0) from we_user_behavior_data WHERE DATE_FORMAT(stat_time,'%Y-%m-%d')=date

                 
           ) as applyCnt
        FROM
        sys_dim_date
        WHERE DATE_FORMAT(date,'%Y-%m-%d') BETWEEN ? and ? 
        ORDER BY date ASC["2025-07-19","2025-07-25"]
