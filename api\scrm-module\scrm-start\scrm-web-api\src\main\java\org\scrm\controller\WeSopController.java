package org.scrm.controller;

import org.scrm.base.core.controller.BaseController;
import org.scrm.base.core.domain.AjaxResult;
import org.scrm.base.core.page.TableDataInfo;
import org.scrm.base.utils.SecurityUtils;
import org.scrm.base.utils.StringUtils;
import org.scrm.base.core.domain.model.LoginUser;
import org.scrm.domain.sop.WeSopBase;
import org.scrm.service.IWeSopBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
@RestController
@RequestMapping("/sop")
public class WeSopController extends BaseController {

    @Autowired
    private IWeSopBaseService iWeSopBaseService;



    /**
     * 获取sop列表
     * @param weSopBase  baseType sop基础类型(1:客户sop;2:客群sop)
     * @return
     */
    @GetMapping("/findWeSopLists")
    public TableDataInfo findWeSopLists(@Validated WeSopBase weSopBase){

        return iWeSopBaseService.findWeSopListsVo(weSopBase);

    }


    /**
     * 获取统计详情tab
     * @param sopBaseId
     * @return
     */
    @GetMapping("/findWeSopDetailTab/{sopBaseId}")
    public AjaxResult findWeSopDetailTabVo(@PathVariable String sopBaseId){

        return AjaxResult.success(
                iWeSopBaseService.findWeSopDetailTabVo(sopBaseId)
        );
    }


    /**
     * 获取客户内容执行列表
     * @param weUserId 执行员工id
     * @param externalUserid 客户id
     * @param executeSubState 执行子状态 1:当前sop下一条任务信息都未推送;2:当前sop下信息推送完
     * @param sopBaseId sop主键
     * @param executeTargetId
     * @return
     */
    @GetMapping("/findCustomerExecuteContent")
    public AjaxResult findCustomerExecuteContent(String weUserId, String externalUserid, Integer executeSubState, String sopBaseId,String executeTargetId){


        return AjaxResult.success(
                iWeSopBaseService.findCustomerExecuteContent(weUserId,externalUserid,executeSubState,sopBaseId,executeTargetId)
        );
    }


    /**
     * (移动端)侧边栏需要推送的个人sop
     * @param targetId
     * @param executeSubState
     * @param businessType 业务类型，7为拨打电话SOP
     * @param sopBaseId SOP基础ID（用于拨打电话SOP的独立显示）
     * @param executeTargetAttachId 执行目标附件ID（用于获取特定时间段的数据）
     * @return
     */
    @GetMapping("/findCustomerSopContent")
    public AjaxResult findCustomerSopContent(String targetId, Integer executeSubState, Integer businessType, String sopBaseId, String executeTargetAttachId){
        try {
            // 获取当前登录用户信息
//            LoginUser loginUser = SecurityUtils.getLoginUser();
//            if (loginUser == null || loginUser.getSysUser() == null) {
//                return AjaxResult.error("用户未登录或登录信息无效");
//            }

            String executeWeUserId ="GanSha";// loginUser.getSysUser().getWeUserId();
            if (StringUtils.isEmpty(executeWeUserId)) {
                return AjaxResult.error("用户企微ID不能为空");
            }

            if (businessType != null && businessType == 7) {

                // 拨打电话SOP必须指定sopBaseId（新功能，无需向后兼容）
                if (StringUtils.isEmpty(sopBaseId)) {
                    return AjaxResult.error("sopBaseId不能为空");
                }

                return AjaxResult.success(
                        iWeSopBaseService.findPhoneCallSopContentBySopBaseId(executeWeUserId, executeSubState, sopBaseId, executeTargetAttachId)
                );
            } else {
                // 普通客户SOP：需要指定客户ID
                return AjaxResult.success(
                        iWeSopBaseService.findCustomerSopContent(executeWeUserId, targetId, executeSubState)
                );
            }
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }


    /**
     *  获取客群内容执行列表
     * @param chatId 群id
     * @param executeState 执行状态
     * @param sopBaseId sop主键
     * @return
     */
    @GetMapping("/findGroupExecuteContent")
    public AjaxResult findGroupExecuteContent(String chatId, Integer executeState, String sopBaseId,String executeTargetId){

        return AjaxResult.success(
                iWeSopBaseService.findGroupExecuteContent(chatId,executeState,sopBaseId,executeTargetId)
        );
    }


    /**
     *  (移动端)侧边栏需要推送的客群sop
     * @param chatId
     * @param executeSubState
     * @return
     */
    @GetMapping("/findGroupSopContent")
    public AjaxResult findGroupSopContent(String chatId, Integer executeSubState){

        return AjaxResult.success(
                iWeSopBaseService.findGroupSopContent(1,chatId,executeSubState)
        );
    }


    /**
     *  获取今日客户相关sop信息
     *  isExpiringSoon true 即将过期的sop false 当日sop
     * @return
     */
    @GetMapping("/findWeCustomerSop")
    public AjaxResult findTodayWeCustomerSop(@RequestParam(defaultValue = "false") Boolean isExpiringSoon){


        return AjaxResult.success(
                iWeSopBaseService.findWeCustomerSopToBeSent(isExpiringSoon)
        );
    }

    /**
     * 获取今日客群相关sop
     *  isExpiringSoon true 即将过期的sop false 当日sop
     * @return
     */
    @GetMapping("/findTodayGroupSop")
    public AjaxResult findTodayGroupSop(@RequestParam(defaultValue = "false") Boolean isExpiringSoon){

        return AjaxResult.success(
                iWeSopBaseService.findWeGroupSopToBeSent(isExpiringSoon)
        );
    }




    /**
     *  sop统计详情客群列表
     * @param sopBaseId
     * @param groupName
     * @param executeState
     * @param weUserId
     * @return
     */
    @RequestMapping("/findWeSopDetailGroup")
    public TableDataInfo findWeSopDetailGroup(String sopBaseId,String groupName,Integer executeState, String weUserId){
        startPage();
        return getDataTable(
                iWeSopBaseService.findWeSopDetailGroup(sopBaseId, groupName, executeState, weUserId)
        );
    }




    /**
     * sop统计详情客户列表
     * @param sopBaseId
     * @param customerName
     * @param executeState
     * @param weUserId
     * @return
     */
    @GetMapping("/findWeSopDetailCustomer")
    public TableDataInfo findWeSopDetailCustomer(String sopBaseId, String customerName, Integer executeState, String weUserId){
        startPage();
        return getDataTable(
                iWeSopBaseService.findWeSopDetailCustomer(sopBaseId,customerName,executeState,weUserId)
        );
    }


    /**
     * 创建sop
     * @param weSopBase
     */
    @PostMapping("/createWeSop")
    public AjaxResult createWeSop(@RequestBody WeSopBase weSopBase){
        iWeSopBaseService.createWeSop(weSopBase);
        return AjaxResult.success(
        );
    }



    /**
     * 更新sop
     * @param weSopBase
     */
    @PutMapping("/updateWeSop")
    public AjaxResult updateWeSop(@RequestBody WeSopBase weSopBase){
        WeSopBase weSopBasee = iWeSopBaseService.getById(weSopBase.getId());
        if(weSopBasee.getSopState().equals(1)){
           return AjaxResult.error("当前sop为执行中不可修改");
        }

        iWeSopBaseService.updateWeSop(weSopBase);
        return AjaxResult.success();
    }


    /**
     * 获取sop详情
     * @param sopBaseId
     * @return
     */
    @GetMapping("/getDetail/{sopBaseId}")
    public AjaxResult<WeSopBase> findWeSopBaseBySopBaseId(@PathVariable Long sopBaseId){

        return AjaxResult.success(
                iWeSopBaseService.findWeSopBaseBySopBaseId(sopBaseId)
        );
    }

    /**
     * 通过id列表批量删除sop
     *
     * @param ids id列表
     * @return 结果
     */
    @DeleteMapping(path = "/{ids}")
    public AjaxResult batchDeleteSop(@PathVariable("ids") Long[] ids) {
        iWeSopBaseService.removeWeSoPBySopId(Arrays.asList(ids));
        return AjaxResult.success();
    }


    /**
     * 启用停用sop
     * @return
     */
    @PutMapping("/updateSopState")
    public AjaxResult startOrStopSop(@RequestBody WeSopBase weSopBase){

        iWeSopBaseService.updateSopState(String.valueOf(weSopBase.getId()),weSopBase.getSopState());

        return AjaxResult.success();
    }


    /**
     * 执行sop
     * @param executeTargetAttachId
     * @return
     */
    @PostMapping("/executeSop/{executeTargetAttachId}")
    public AjaxResult executeSop(@PathVariable String executeTargetAttachId){
        iWeSopBaseService.executeSop(executeTargetAttachId);

        return AjaxResult.success();
    }


    /**
     * 企业发送方式结果同步
     * @param sopBaseId
     * @return
     */
    @GetMapping("/synchSopExecuteResult/{sopBaseId}")
    public AjaxResult synchSopExecuteResult(@PathVariable("sopBaseId") String sopBaseId){
        iWeSopBaseService.synchSopExecuteResultForWeChatPushType(sopBaseId);

        return AjaxResult.success();
    }




}

