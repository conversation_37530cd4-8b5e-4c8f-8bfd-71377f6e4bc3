{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753430263537}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}